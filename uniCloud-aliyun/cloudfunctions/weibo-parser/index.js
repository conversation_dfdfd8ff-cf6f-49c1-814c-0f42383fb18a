'use strict';

/**
 * 微博视频解析器
 * 支持微博视频链接解析和去水印处理
 */

// 全局调试配置
let DEBUG = false;

/**
 * 清理和美化微博文案
 * @param {string} text 原始文案
 * @returns {string} 美化后的文案
 */
function cleanWeiboText(text) {
  if (!text || typeof text !== 'string') return text;
  
  let cleanedText = text;
  
  // 1. 移除HTML标签，但保留内容
  cleanedText = cleanedText.replace(/<[^>]+>/g, '');
  
  // 2. 美化话题标签 #话题# → 🏷️#话题#
  cleanedText = cleanedText.replace(/#([^#]+)#/g, '🏷️#$1#');
  
  // 3. 美化@用户 @用户名 → 👤@用户名
  cleanedText = cleanedText.replace(/@([^\s]+)/g, '👤@$1');
  
  // 4. 美化链接 [链接] → 🔗链接
  cleanedText = cleanedText.replace(/\[链接\]/g, '🔗链接');
  
  // 5. 美化表情符号（如果被编码了）
  cleanedText = cleanedText
    .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    .replace(/\\n/g, '\n')
    .replace(/\\r/g, '\r')
    .replace(/\\t/g, '\t');
  
  // 6. 清理多余的空白字符
  cleanedText = cleanedText
    .replace(/\s+/g, ' ')  // 多个空格合并为一个
    .replace(/\n\s*\n/g, '\n')  // 多个换行合并
    .trim();
  
  // 7. 如果文案太长，截断并添加省略号
  if (cleanedText.length > 200) {
    cleanedText = cleanedText.substring(0, 200) + '...';
  }
  
  return cleanedText;
}

// 通用请求头
const COMMON_HEADERS = {
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Accept-Encoding': 'gzip, deflate, br',
  'Connection': 'keep-alive',
  'Upgrade-Insecure-Requests': '1',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache'
};

// PC端请求头 - 使用最新版本浏览器UA
const DESKTOP_HEADERS = {
  ...COMMON_HEADERS,
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'Referer': 'https://weibo.com/',
  'Sec-Fetch-Dest': 'document',
  'Sec-Fetch-Mode': 'navigate',
  'Sec-Fetch-Site': 'same-origin',
  'Sec-Fetch-User': '?1',
  'Sec-Ch-Ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
  'Sec-Ch-Ua-Mobile': '?0',
  'Sec-Ch-Ua-Platform': '"macOS"'
};

// 已移除备用UA策略，仅保留桌面端头
// 移动端请求头（用于尝试拿 H5 直链）- 更新为最新版本
const MOBILE_HEADERS = {
  ...COMMON_HEADERS,
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
  'Referer': 'https://m.weibo.cn/',
  'Sec-Fetch-Dest': 'document',
  'Sec-Fetch-Mode': 'navigate',
  'Sec-Fetch-Site': 'same-origin',
  'Sec-Fetch-User': '?1',
  'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Safari";v="17"',
  'Sec-Ch-Ua-Mobile': '?1',
  'Sec-Ch-Ua-Platform': '"iOS"'
};

exports.main = async (event, context) => {
  const { link, debug = false } = event || {};
  DEBUG = !!debug;

  if (!link || typeof link !== 'string') {
    return { success: false, message: '链接不能为空' };
  }

  try {
    if (DEBUG) console.log('开始解析微博链接:', link);
    
    // 清理链接
    const cleanedLink = cleanWeiboUrl(link);
    if (DEBUG) console.log('清理后的链接:', cleanedLink);

    // 首先尝试解析微博内容（视频或图文）
    const result = await parseWeiboContent(cleanedLink);
    
    if (result.success) {

      return {
        success: true,
        data: result.data,
        timestamp: new Date().toISOString(),
        version: '微博解析器 v2.0'
      };
    } else {
      if (DEBUG) console.log('微博解析失败:', result.message);
      return result;
    }

  } catch (error) {
    console.error('微博解析器异常:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: error.toString()
    };
  }
};

/**
 * 清理微博链接
 */
function cleanWeiboUrl(url) {
  if (!url) return url;
  
  // 移除多余的参数和清理链接
  let cleaned = url.trim();
  
  // 如果是短链接，保持原样
  if (cleaned.includes('weibo.com/tv/') || cleaned.includes('video.weibo.com/show')) {
    return cleaned;
  }
  
  // 移除不必要的参数
  cleaned = cleaned.replace(/[?&]from=.*?(?=[?&]|$)/, '');
  cleaned = cleaned.replace(/[?&]type=.*?(?=[?&]|$)/, '');
  
  return cleaned;
}

/**
 * 解析微博内容（视频或图文）
 */
async function parseWeiboContent(url) {
  try {
    // 先获取重定向后的真实URL
    const realUrl = await getRealUrl(url);
    if (DEBUG) console.log('获取到真实URL:', realUrl);
    
    // 提前提取ID，便于后续所有通道使用
    const fullOid = extractFullOidFromUrl(realUrl);
    const weiboId = fullOid.includes(':') ? fullOid.split(':')[1] : fullOid;
    
    //基于URL特征检测内容类型
    if (DEBUG) console.log('TV API未返回有效数据，根据URL特征检测内容类型');
    const urlBasedType = detectWeiboContentType(url);
    if (DEBUG) console.log('🔍 基于URL检测到内容类型:', urlBasedType);
    if (urlBasedType === 'image') {
      // 图文解析
      if (DEBUG) console.log('🖼️ 根据URL特征判断为图文，尝试图文解析');
      return await parseWeiboImages(realUrl, weiboId);
    } else if (urlBasedType === 'video') {
      // 视频解析
      if (DEBUG) console.log('🎬 根据URL特征判断为视频，尝试视频解析');
      return await parseWeiboVideo(realUrl);
    } else {
      return { success: false, message: '未知类型，解析失败' };
    }
    
  } catch (error) {
    throw error;
  }
}

/**
 * 检测微博内容类型
 */
function detectWeiboContentType(url) {
  // 视频链接特征
  if (url.includes('/tv/') || url.includes('video.weibo.com') || url.includes('/show?fid=')) {
    return 'video';
  }
  // 图文链接特征：weibo.com/用户ID/微博ID 格式
  if (/weibo\.com\/\d+\/\d+/.test(url)) {
    return 'image';
  }
  // 未知类型
  return 'unknown';
}

/**
 * 解析微博图文内容（包括Live Photo）
 */
async function parseWeiboImages(url, weiboId) {
  try {
    if (DEBUG) console.log('🖼️ 开始解析微博图文内容');
    
    const result = {
      images: [],
      livePhotos: [],
      title: '',
      author: '',
      content: '',
      coverUrl: '',
      hasVideo: false,
      type: 'image'
    };
    
    // 解析图片信息
    const imageData = await extractWeiboImages(weiboId);
    result.images = imageData.images;
    result.livePhotos = imageData.livePhotos;
    result.coverUrl = imageData.coverUrl;
    result.hasVideo = imageData.livePhotos.length > 0; // 如果有Live Photo则标记为有视频
    
    // 如果从专门API获取到了更完整的信息，使用它们覆盖基础信息
    if (imageData.detailFromApi) {
      result.title = imageData.detailFromApi.title || result.title;
      result.author = imageData.detailFromApi.author || result.author;
      result.content = imageData.detailFromApi.content || result.content;
    }
    
    
    return {
      success: true,
      data: {
        ...result,
        method: 'weibo_image_parser',
        notice: `解析到${result.images.length}张图片${result.livePhotos.length > 0 ? `，包含${result.livePhotos.length}个Live Photo` : ''}`,
        imageUrls: result.images.map(img => {
          if (typeof img === 'string') return img;
          if (img && typeof img === 'object') {
            return img.url || img.imageUrl || img.src || img.large?.url || img.original?.url || '';
          }
          return String(img);
        }).filter(url => url && url !== ''),
        videoUrls: result.livePhotos.map(lp => {
          if (typeof lp === 'string') return lp;
          if (lp && typeof lp === 'object') {
            return lp.videoUrl || lp.video || lp.url;
          }
          return String(lp);
        }).filter(v => v && v !== 'undefined' && v !== 'null'),
        livePhotoVideos: result.livePhotos
          .filter(lp => lp.type === 'livephoto' || lp.type === 'video')
          .map(lp => lp.videoUrl || lp.video || lp.url)
          .filter(url => url),
      }
    };
    
  } catch (error) {

    return { success: false, message: error.message };
  }
}

/**
 * 从微博数据中提取图片信息（简化版本）
 */
async function extractWeiboImages(weiboId) {
  const result = {
    images: [],
    livePhotos: [],
    coverUrl: ''
  };

  try {
    if (DEBUG) console.log('🔍 开始提取微博图文数据');

    // 直接从HTML+JSON获取图文数据（统一方法）
    const imageData = await tryGetImagesFromAlternativeApi(weiboId);
    if (imageData.success) {
      result.images.push(...imageData.images);
      result.livePhotos.push(...imageData.livePhotos);

      // 更新文本信息
      result.detailFromApi = {
        title: imageData.title || '微博图文',
        author: imageData.author || '微博用户',
        content: imageData.content || '暂无描述'
      };

      if (DEBUG) console.log('✅ 图文数据获取成功:', {
        图片数量: result.images.length,
        视频数量: result.livePhotos.length
      });
    } else {
      if (DEBUG) console.log('❌ 图文数据获取失败');
    }

    // 设置封面图片（使用第一张图片）
    if (result.images.length > 0) {
      result.coverUrl = result.images[0].url;
    }

  } catch (error) {
    if (DEBUG) console.log('❌ 提取图片信息失败:', error.message);
  }

  return result;
}
/**
 * 获取微博图文数据（简化版本，类似小红书解析器）
 */
async function tryGetImagesFromAlternativeApi(weiboId) {
  const result = {
    images: [],
    livePhotos: [],
    title: '',
    author: '',
    content: '',
    success: false
  };

  try {
    // 获取移动端页面HTML
    const mobileDetailUrl = `https://m.weibo.cn/status/${weiboId}`;

    if (DEBUG) console.log('🌐 请求移动端页面:', mobileDetailUrl);

    const response = await uniCloud.httpclient.request(mobileDetailUrl, {
      method: 'GET',
      timeout: 20000,
      dataType: 'text',
      followRedirect: true
    });

    if (response.status === 200 && response.data) {
      if (DEBUG) console.log('📄 页面获取成功，开始提取JSON数据');

      const html = response.data;

      // 从HTML中提取JSON数据
      const jsonData = extractJsonFromMobileHtml(html);
      if (jsonData && Object.keys(jsonData).length > 0) {
        if (DEBUG) console.log('✅ 找到JSON数据，开始解析');

        // 从JSON中提取图片和视频
        const mediaData = extractImagesFromMobileJson(jsonData);
        result.images.push(...mediaData.images);
        result.livePhotos.push(...mediaData.livePhotos);

        // 从JSON中提取文本信息
        const textData = extractTextFromMobileJson(jsonData);
        result.title = textData.title;
        result.author = textData.author;
        result.content = textData.content;

        if (DEBUG) console.log('📊 数据提取结果:', {
          图片数量: mediaData.images.length,
          视频数量: mediaData.livePhotos.length,
          标题: textData.title,
          作者: textData.author
        });

        result.success = true;
      } else {
        if (DEBUG) console.log('❌ 未找到JSON数据');
      }
    }

  } catch (error) {
    if (DEBUG) console.log('❌ 获取微博数据失败:', error.message);
  }

  return result;
}

/**
 * 从移动端HTML中提取JSON数据
 */
function extractJsonFromMobileHtml(html) {
  let jsonData = {};
  
  try {
    // 根据微博移动端实际HTML结构，简化JSON数据模式
    const jsonPatterns = [
      /var\s+\$render_data\s*=\s*(\[[\s\S]*?\])\[0\]\s*\|\|\s*\{\s*\}\s*;/,
    ];
    
    for (const [index, pattern] of jsonPatterns.entries()) {
      const match = html.match(pattern);
      if (match) {
        try {
          let rawJson = match[1];
          const parsed = JSON.parse(rawJson);
          if (Array.isArray(parsed)) {
            // 处理数组格式的数据
            parsed.forEach(item => {
              if (item && typeof item === 'object') {
                Object.assign(jsonData, item);
              }
            });
          } else if (parsed && typeof parsed === 'object') {
            Object.assign(jsonData, parsed);
          }
          break; // 找到一个就够了
        } catch (e) {
        }
      }
    }
  } catch (error) {
  }
  return jsonData;
}

/**
 * 从移动端JSON中提取图片数据
 */
function extractImagesFromMobileJson(jsonData) {
  const result = {
    images: [],
    livePhotos: []
  };
  
  try {
    // 🎯 优先处理$render_data格式的数据
    if (jsonData.status) {
      const status = jsonData.status;

      // 检查是否有主要视频数据（用于判断内容类型，但不提取，避免重复）
      const hasMainVideo = (status.media_info || status.urls) &&
                          (status.urls && Object.keys(status.urls).some(key => key.includes('mp4')));

      if (hasMainVideo && DEBUG) {
        console.log('🎬 检测到主要视频数据结构，将从pics数组中提取');
      }

      // 然后从pics数组提取图片、视频和Live Photo
      if (status.pics && Array.isArray(status.pics)) {
        status.pics.forEach((pic, index) => {
          // 提取图片（包括视频的封面图）
          if (pic.large && pic.large.url) {
            const imageUrl = upgradeToHttpsSync(pic.large.url);
            if (imageUrl) {
              result.images.push({
                url: imageUrl,
                width: pic.large.geo ? parseInt(pic.large.geo.width) : 1080,
                height: pic.large.geo ? parseInt(pic.large.geo.height) : 1920,
                type: pic.type === 'livephoto' ? 'Live Photo图片' :
                      pic.type === 'video' ? '视频封面' : '普通图片'
              });
            }
          }

          // 提取视频（包括普通视频和Live Photo）
          if (pic.videoSrc) {
            const imageUrl = pic.large ? upgradeToHttpsSync(pic.large.url) : '';

            if (pic.type === 'video') {
              // 普通视频
              const videoUrl = upgradeToHttpsSync(pic.videoSrc);
              if (videoUrl) {
                result.livePhotos.push({
                  videoUrl: videoUrl,
                  imageUrl: imageUrl,
                  duration: pic.duration ? pic.duration * 1000 : 0, // 转换为毫秒
                  type: 'video'
                });
              }
            } else if (pic.type === 'livephoto') {
              // Live Photo视频
              const videoUrl = decodeLivePhotoVideoUrl(pic.videoSrc);
              if (videoUrl && imageUrl) {
                result.livePhotos.push({
                  videoUrl: videoUrl,
                  imageUrl: imageUrl,
                  duration: 3000, // Live Photo一般3秒
                  type: 'livephoto'
                });
              }
            }
          }
        });
      }
      
    }
  } catch (error) {
    if (DEBUG) console.log('❌ JSON图片提取失败:', error.message);
  }
  
  return result;
}

/**
 * 从移动端JSON中提取文本信息
 */
function extractTextFromMobileJson(jsonData) {
  const result = {
    title: '',
    author: '',
    content: ''
  };
  
  try {
    // 🎯 优先处理$render_data格式的数据
    if (jsonData.status) {
      const status = jsonData.status;

      // 提取作者
      if (status.user && status.user.screen_name) {
        result.author = cleanWeiboText(status.user.screen_name);
      }

      // 提取标题和内容（从page_info字段）
      if (status.page_info) {
        // 标题：使用content1
        if (status.page_info.content1) {
          result.title = cleanWeiboText(status.page_info.content1);
        }

        // 正文：使用content2，去掉短链接
        if (status.page_info.content2) {
          let content = cleanWeiboText(status.page_info.content2);
          // 去掉微博短链接 (http://t.cn/xxx 格式)
          content = content.replace(/\s*https?:\/\/t\.cn\/\w+\s*/g, '').trim();
          result.content = content;
        }
      }

      // 如果page_info没有内容，回退到text字段
      if (!result.content && status.text) {
        result.content = cleanWeiboText(status.text);
      }
      
      if (DEBUG) {
        console.log('📝 从$render_data.status提取文本:', {
          标题: result.title,
          作者: result.author,
          原始文本长度: status.text ? status.text.length : 0
        });
      }
    }
    
    // 如果没有标题，使用内容前30字符作为标题
    if (!result.title && result.content) {
      const shortTitle = result.content.substring(0, 30);
      if (shortTitle.length > 5) {
        result.title = shortTitle + (result.content.length > 30 ? '...' : '');
      }
    }
  } catch (error) {
    if (DEBUG) console.log('❌ JSON文本提取失败:', error.message);
  }
  
  return result;
}

/**
 * 解析微博视频
 */
async function parseWeiboVideo(url) {
  try {
    // 1. 先获取重定向后的真实URL
    const realUrl = await getRealUrl(url);
    if (DEBUG) console.log('获取到真实URL:', realUrl);
  
    // 2. 先获取TV组件数据（用于防盗链参数和mid提取）
    const tvApiResult = await tryParseViaTvComponent(realUrl);

    // 如果TV API已经成功，直接返回
    if (tvApiResult && tvApiResult.success) {
      if (DEBUG) console.log('TV组件接口解析成功，返回结果');
      return tvApiResult;
    }

    if (DEBUG) console.log('TV组件接口未返回有效数据，无其他可用解析方法');
    return { success: false, message: 'TV组件接口解析失败，无其他可用方法' };
    
  } catch (error) {
    throw error;
  }
}

/**
 * 获取重定向后的真实URL
 */
async function getRealUrl(shareUrl) {
  try {
    const response = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false,  // 不自动跟随重定向
      timeout: 15000,
      headers: DESKTOP_HEADERS
    });

    const location = response.headers.location || response.headers.Location;
    const realUrl = location || shareUrl;
    
    if (DEBUG) console.log('获取真实URL:', realUrl);
    return realUrl;
  } catch (error) {
    console.error('获取真实URL失败:', error);
    return shareUrl;
  }
}

function extractWeiboIdFromUrl(url) {
  if (!url) return '';
  
  console.log('提取微博ID，原始URL:', url);
  
  // 首先尝试从URL参数中解码原始URL（处理重定向情况）
  if (url.includes('passport.weibo.com/visitor/visitor') && url.includes('url=')) {
    try {
      const urlParam = url.match(/[?&]url=([^&]+)/);
      if (urlParam) {
        const decodedUrl = decodeURIComponent(urlParam[1]);
        if (DEBUG) console.log('🔍 从重定向URL中解码原始URL:', decodedUrl);
        return extractWeiboIdFromUrl(decodedUrl); // 递归调用处理原始URL
      }
    } catch (e) {
      if (DEBUG) console.log('解码重定向URL失败:', e.message);
    }
  }
  
  // 匹配 1034:xxxxxxxx (保持完整ID)
  const fidMatch = url.match(/1034:(\d{13,})/);
  if (fidMatch) {
    console.log('提取到fid格式ID(仅数字部分):', fidMatch[1]);
    return fidMatch[1];
  }
  
  // 匹配 /detail/xxxxxxxx 或 /status/xxxxxxxx
  const mDetail = url.match(/\/(?:detail|status)\/(\d{13,})/);
  if (mDetail) {
    console.log('提取到detail格式ID:', mDetail[1]);
    return mDetail[1];
  }
  
  // 匹配 weibo.com/用户ID/微博ID 格式（图文链接常见格式）
  const userPostMatch = url.match(/weibo\.com\/(\d+)\/(\d{13,})/);
  if (userPostMatch) {
    const userId = userPostMatch[1];
    const postId = userPostMatch[2];
    console.log('提取到用户发布格式ID:', postId, '用户ID:', userId);
    return postId;
  }
  
  // 尝试从URL路径中提取其他格式的ID
  const generalMatch = url.match(/\/(\d{13,})/);
  if (generalMatch) {
    console.log('提取到通用格式ID:', generalMatch[1]);
    return generalMatch[1];
  }
  
  console.log('未能提取到有效ID');
  return '';
}

// 提取完整的 OID（形如 1034:xxxxxxxx），若只提取到纯数字则补全前缀
function extractFullOidFromUrl(url) {
  console.log('传入后的url',url);
  if (!url) return '';
  
  // 首先尝试从URL参数中解码原始URL（处理重定向情况）
  if (url.includes('passport.weibo.com/visitor/visitor') && url.includes('url=')) {
    try {
      const urlParam = url.match(/[?&]url=([^&]+)/);
      if (urlParam) {
        const decodedUrl = decodeURIComponent(urlParam[1]);
        if (DEBUG) console.log('🔍 OID提取：从重定向URL中解码原始URL:', decodedUrl);
        return extractFullOidFromUrl(decodedUrl); // 递归调用处理原始URL
      }
    } catch (e) {
      if (DEBUG) console.log('OID提取：解码重定向URL失败:', e.message);
    }
  }
  
  const fullMatch = url.match(/1034:(\d{13,})/);
  if (fullMatch) {
    return `1034:${fullMatch[1]}`;
  }
  const idOnly = extractWeiboIdFromUrl(url);
  if (idOnly) {
    return `1034:${idOnly}`;
  }
  return '';
}

/**
 * 通过TV组件接口尝试解析
 */
async function tryParseViaTvComponent(url) {
  try {
    if (DEBUG) console.log(`🎯 尝试通过TV组件API解析: ${url}`);
    // 从URL中提取完整OID（优先 1034:xxxxxxxx）
    console.log('传入前的url',url);
    const fullOid = extractFullOidFromUrl(url);
    if (!fullOid) {
      throw new Error('无法从URL中提取微博ID');
    }
    const postData = `data=${encodeURIComponent(JSON.stringify({
      "Component_Play_Playinfo": {
        "oid": fullOid
      }
    }))}`;

    const tvHeaders = {
      ...DESKTOP_HEADERS,
      'Accept': 'application/json, text/plain, */*',
      'Content-Type': 'application/x-www-form-urlencoded',
      'Referer': `https://weibo.com/tv/show/${fullOid}`,
      'X-Requested-With': 'XMLHttpRequest',
      'Origin': 'https://weibo.com',
      'Cookie': generateGuestCookie()
    };

    const requestUrl = `https://weibo.com/tv/api/component?page=${encodeURIComponent(`/tv/show/${fullOid}`)}`;

    if (DEBUG) console.log('TV API请求URL:', requestUrl);

    const response = await uniCloud.httpclient.request(
      requestUrl,
      {
        method: 'POST',
        headers: tvHeaders,
        data: postData,
    timeout: 15000,
    dataType: 'json',
    followRedirect: true,
    maxRedirects: 3
      }
    );

    if (response.status !== 200) {
      throw new Error(`TV API状态码异常: ${response.status}`);
    }
    const responseData = response.data;
    if (responseData && responseData.code === "100000" && responseData.data && responseData.data.Component_Play_Playinfo) {
      const playInfo = responseData.data.Component_Play_Playinfo;
      if (DEBUG) console.log('成功获取播放信息:', Object.keys(playInfo));

      let videoUrl = '';
      const qualityUrls = {};
      const allVideoUrls = [];

      if (playInfo.urls && typeof playInfo.urls === 'object') {
        for (const [quality, url] of Object.entries(playInfo.urls)) {
          const fullUrl = typeof url === 'string' ? (url.startsWith('//') ? `https:${url}` : url) : '';
          if (!fullUrl) continue;
          qualityUrls[quality] = fullUrl;
          allVideoUrls.push(fullUrl);
          if (DEBUG) console.log(`${quality}视频URL:`, fullUrl.substring(0, 120) + '...');
        }

        // 初步按清晰度挑选一个候选（非最终）
        const qualityPriority = ['高清 1080P', '高清 720P', '清晰 480P', '流畅 360P'];
        for (const quality of qualityPriority) {
          if (qualityUrls[quality] && !videoUrl) {
            videoUrl = qualityUrls[quality];
          }
        }
        if (!videoUrl && allVideoUrls.length > 0) {
          videoUrl = allVideoUrls[0];
        }
      }

      if (videoUrl) {
        if (DEBUG) console.log('TV组件接口解析成功，视频URL:', videoUrl);
        const coverUrl = playInfo.cover_image ? (playInfo.cover_image.startsWith('//') ? `https:${playInfo.cover_image}` : playInfo.cover_image) : '';

        let content = cleanWeiboText(playInfo.text);

        return {
          success: true,
          data: {
            title: cleanWeiboText(playInfo.title) || '微博视频',
            author: cleanWeiboText(playInfo.author) || '未知用户',
            videoUrl: videoUrl,
            coverUrl: coverUrl,
            content: content || '暂无描述',
            hasVideo: true,
            selectedQuality: videoUrl ? Object.keys(qualityUrls).find(q => qualityUrls[q] === videoUrl) || '未知' : '未知',
            qualityUrls: qualityUrls,
            videoUrls: allVideoUrls,
            method: 'tv_component_api',
          },
          rawData: responseData  // 添加原始数据用于提取mid
        };
      }
    }

    if (DEBUG) console.log('❌ TV组件API中未找到视频信息');
    return { success: false, message: 'TV组件API中未找到视频信息' };

  } catch (error) {
    if (DEBUG) console.log('❌ TV组件API解析异常:', error.message);
    return { success: false, message: error.message };
  }
}

/**
 * 生成动态访客Cookie，模拟真实浏览器访问
 */
function generateGuestCookie() {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2);
  
  // 动态生成访客Cookie，避免被识别
  const apache = `${timestamp}_${Math.floor(Math.random() * 1000)}.${Math.floor(Math.random() * 1000)}.${timestamp}`;
  const sinaglobal = apache;
  const ulv = `${timestamp}:1:1:1:${apache}:`;
  
  return `SUB=_2AkMT${randomId}; SUBP=${Date.now().toString(36)}; _s_tentry=passport.weibo.com; Apache=${apache}; SINAGLOBAL=${sinaglobal}; ULV=${ulv}; WBPSESS=${randomId}${Math.random().toString(36).substring(2)}`;
}

/**
 * 简单的协议升级（用于图片处理）
 */
function upgradeToHttpsSync(url) {
  if (!url || typeof url !== 'string') return '';
  
  // 如果已经是https或//开头，直接处理
  if (url.startsWith('https://')) return url;
  if (url.startsWith('//')) return `https:${url}`;
  if (url.startsWith('http://')) return url.replace(/^http:\/\//i, 'https://');
  
  return url;
}

/**
 * 简单转换微博Live Photo视频URL格式，保持包装URL不变，只替换.mov为.mp4
 */
function decodeLivePhotoVideoUrl(url) {
  if (!url || typeof url !== 'string') return url;
  
  try {
    // 检查是否是微博视频播放接口格式
    if (url.includes('video.weibo.com/media/play') && url.includes('livephoto=')) {
      // 不解码，只替换参数中的.mov为.mp4
      if (url.includes('.mov')) {
        const mp4Url = url.replace(/\.mov/g, '.mp4');
        if (DEBUG) console.log(`🎬 Live Photo包装URL格式转换: .mov -> .mp4`);
        if (DEBUG) console.log(`🎬 原始: ${url.substring(0, 100)}...`);
        if (DEBUG) console.log(`🎬 转换: ${mp4Url.substring(0, 100)}...`);
        return mp4Url;
      }
      return url;
    }
    
    // 如果不是包装格式，检查是否是.mov结尾，尝试转换为.mp4
    if (url.endsWith('.mov')) {
      const mp4Url = url.replace(/\.mov$/, '.mp4');
      if (DEBUG) console.log(`🎬 直接格式转换: .mov -> .mp4`);
      return upgradeToHttpsSync(mp4Url);
    }
    
    // 其他情况直接返回升级后的HTTPS链接
    return upgradeToHttpsSync(url);
  } catch (error) {
    if (DEBUG) console.log('⚠️ Live Photo URL转换失败:', error.message);
    return url;
  }
}









