<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <link rel="dns-prefetch" href="//h5.sinaimg.cn">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <title>微博</title>
    <meta content="随时随地发现新鲜事！微博带你欣赏世界上每一个精彩瞬间，了解每一个幕后故事。分享你想表达的，让全世界都能听到你的心声！" name="description">
        <link rel="stylesheet" href="//h5.sinaimg.cn/marvel/v1.4.11/css/lib/base.css">
    <link rel="stylesheet" href="//h5.sinaimg.cn/marvel/v1.4.11/css/card/cards.css">
        <link rel="manifest" href="/manifest.json">
    <script>!function(e){var a,i=navigator.userAgent.toLowerCase(),n=document.documentElement,t=parseInt(n.clientWidth);if(/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)||i.indexOf("like mac os x")>0){var s=/os [\d._]*/gi,o=i.match(s);a=(o+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".")}var r=a+"";"undefined"!=r&&r.length>0&&(a=parseInt(r),a>=8&&(375==t||667==t||320==t||568==t||480==t)?n.className="iosx2":(a>=8&&414==t||736==t)&&(n.className="iosx3")),/(Android)/i.test(navigator.userAgent)&&(n.className="android")}(window);</script>
        <style>html, body {margin: 0 !important;padding: 0 !important;}html, body, #app {height: 100%;}[v-cloak] {display: none;}.wb-item-wrap .card9.card{margin:0}.f-weibo .m-img-box{background-color:#e6e6e6}.empty-bg{width:100%;background-color:#e6e6e6;height:.375rem}.inline-block{display:inline-block}.txt-margin{margin:0 0 1rem 0}.width-min{width:4.375rem}.anim-load{animation:load .5s ease-out;-moz-animation:load .5s ease-out;-webkit-animation:load .5s ease-out;-o-animation:load .5s ease-out}@keyframes load{0%{background-color:#fff}100%{background-color:#e6e6e6}}@-moz-keyframes load{0%{background-color:#fff}100%{background-color:#e6e6e6}}@-webkit-keyframes load{0%{background-color:#fff}100%{background-color:#e6e6e6}}.f-more{letter-spacing:.1rem}.f-weibo .f-card-title{margin:-1rem -1rem .5rem -1rem;padding:0 1rem;border-width:0}.f-weibo .m-avatar-box .m-img-box .m-icon{font-size:14px}.iosx3 .card9 .f-card-title{border-width:0}.iosx2 .card9 .f-card-title{border-width:0}.f-weibo.card9{border-bottom:1px solid #e6e6e6}.iosx3 .f-weibo.card9{border-bottom:.36px solid #e6e6e6}.iosx2 .f-weibo.card9{border-bottom:.5px solid #e6e6e6}.f-weibo.card9>.card-wrap{margin-left:.75rem;margin-right:.75rem}.f-weibo.card9.m-panel{border-top-width:0}.f-weibo.card .card-wrap .f-col-wrap{padding:0 .9375rem}.f-weibo.card9 .m-box-col{min-width:0}.f-weibo.card9 .weibo-top{padding:0 0 0 .25rem}.f-weibo.card9 .weibo-top .m-box-col .m-icon{margin-left:3px}.f-weibo.card9 .weibo-main .weibo-og{padding:.75rem 0 0 .25rem}.f-weibo.card9 .weibo-main .card-wrap ~ .weibo-rp{margin-top:0.5rem}.f-weibo.card9 .weibo-main .media-b{margin:.625rem 0 -.375rem}.f-weibo.card9 .weibo-main .media-b .m-auto-list{margin:0 0 -.25rem}.f-weibo .weibo-top .m-text-box{margin:.15rem 0 .15rem .5rem}.f-weibo .f-r{float:right}.f-weibo .weibo-main .weibo-og{font-size:.9375rem}.f-weibo .weibo-rp .weibo-text{font-size:.9375rem}.f-weibo .weibo-rp .f-footer-ctrl{padding:0.625rem 0 0}.f-weibo .f-bg-img{background-size:cover;background-repeat:no-repeat;background-position:center;position:absolute;width:100%;height:100%}.f-footer-ctrl{border-top-width:0;height:1.1rem;padding: 1rem .375rem 1rem 0;margin: 0 0.75rem}.f-footer-ctrl .m-diy-btn{color:rgba(40,47,60,0.8);height:100%;float:left}.f-footer-ctrl .m-diy-btn+.m-diy-btn{margin-left:1.6875rem}.f-footer-ctrl .m-diy-btn .m-icon{font-size:16px}.f-footer-ctrl aside{float:right;color:rgba(40,47,60,0.8)}.f-footer-ctrl .m-font{font-size:1rem;vertical-align:middle}.f-footer-ctrl .m-diy-btn h4{font-size:.8125rem;display:inline-block;margin-top:0;margin-left:.25rem}.dbfalls a {color: #333;}.dbfalls .m-icon-like {filter: contrast(0);}</style>
  <style>
  </style>
<link href="//h5.sinaimg.cn/m/weibo-lite/css/app.dc6d3355.css" rel="preload" as="style"><link href="//h5.sinaimg.cn/m/weibo-lite/css/vendor.3b21d4dd.css" rel="preload" as="style"><link href="//h5.sinaimg.cn/m/weibo-lite/js/app.82cbb466.js" rel="preload" as="script"><link href="//h5.sinaimg.cn/m/weibo-lite/js/vendor.7fe5c79c.js" rel="preload" as="script"><link href="//h5.sinaimg.cn/m/weibo-lite/css/vendor.3b21d4dd.css" rel="stylesheet"><link href="//h5.sinaimg.cn/m/weibo-lite/css/app.dc6d3355.css" rel="stylesheet"><link rel="icon" type="image/png" sizes="32x32" href="//h5.sinaimg.cn/m/weibo-lite/favicon-32.png"><link rel="icon" type="image/png" sizes="16x16" href="//h5.sinaimg.cn/m/weibo-lite/favicon-16.png"><link rel="manifest" href="//h5.sinaimg.cn/m/weibo-lite/manifest.json"><meta name="theme-color" content="#F3F3F3"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="default"><meta name="apple-mobile-web-app-title" content="微博Lite"><link rel="apple-touch-icon" href="//h5.sinaimg.cn/m/weibo-lite/appicon.png"><meta name="msapplication-TileColor" content="#000000"></head>

<body>
  <div id="app" class="m-container-max">
    <router-view>
      <div class="wb-item-wrap"><div class="wb-item"><div class="card m-panel card9 f-weibo"><div class="card-wrap"><header class="weibo-top m-box"><div class="m-avatar-box"><a href="javascript:;" class="m-img-box anim-load"></a></div><div class="m-box-dir m-box-col"><div class="m-text-box"><h4 class="m-text-cut f-r"></h4><h3 class="m-text-cut empty-bg width-min inline-block anim-load"></h3></div></div></header><article class="weibo-main"><div class="weibo-og"><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p></div></article><footer class="f-footer-ctrl"><div class="m-diy-btn"><i class="m-font m-font-forward"></i><h4>转发</h4></div><div class="m-diy-btn"><i class="m-font m-font-comment"></i><h4>评论</h4></div><div class="m-diy-btn"><i class="m-icon m-icon-like"></i><h4>赞</h4></div><aside><i class="f-more">...</i></aside></footer></div></div></div></div>
      <div class="wb-item-wrap"><div class="wb-item"><div class="card m-panel card9 f-weibo"><div class="card-wrap"><header class="weibo-top m-box"><div class="m-avatar-box"><a href="javascript:;" class="m-img-box anim-load"></a></div><div class="m-box-dir m-box-col"><div class="m-text-box"><h4 class="m-text-cut f-r"></h4><h3 class="m-text-cut empty-bg width-min inline-block anim-load"></h3></div></div></header><article class="weibo-main"><div class="weibo-og"><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p></div></article><footer class="f-footer-ctrl"><div class="m-diy-btn"><i class="m-font m-font-forward"></i><h4>转发</h4></div><div class="m-diy-btn"><i class="m-font m-font-comment"></i><h4>评论</h4></div><div class="m-diy-btn"><i class="m-icon m-icon-like"></i><h4>赞</h4></div><aside><i class="f-more">...</i></aside></footer></div></div></div></div>
      <div class="wb-item-wrap"><div class="wb-item"><div class="card m-panel card9 f-weibo"><div class="card-wrap"><header class="weibo-top m-box"><div class="m-avatar-box"><a href="javascript:;" class="m-img-box anim-load"></a></div><div class="m-box-dir m-box-col"><div class="m-text-box"><h4 class="m-text-cut f-r"></h4><h3 class="m-text-cut empty-bg width-min inline-block anim-load"></h3></div></div></header><article class="weibo-main"><div class="weibo-og"><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p></div></article><footer class="f-footer-ctrl"><div class="m-diy-btn"><i class="m-font m-font-forward"></i><h4>转发</h4></div><div class="m-diy-btn"><i class="m-font m-font-comment"></i><h4>评论</h4></div><div class="m-diy-btn"><i class="m-icon m-icon-like"></i><h4>赞</h4></div><aside><i class="f-more">...</i></aside></footer></div></div></div></div>
      <div class="wb-item-wrap"><div class="wb-item"><div class="card m-panel card9 f-weibo"><div class="card-wrap"><header class="weibo-top m-box"><div class="m-avatar-box"><a href="javascript:;" class="m-img-box anim-load"></a></div><div class="m-box-dir m-box-col"><div class="m-text-box"><h4 class="m-text-cut f-r"></h4><h3 class="m-text-cut empty-bg width-min inline-block anim-load"></h3></div></div></header><article class="weibo-main"><div class="weibo-og"><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p><p class="empty-bg txt-margin anim-load"></p></div></article><footer class="f-footer-ctrl"><div class="m-diy-btn"><i class="m-font m-font-forward"></i><h4>转发</h4></div><div class="m-diy-btn"><i class="m-font m-font-comment"></i><h4>评论</h4></div><div class="m-diy-btn"><i class="m-icon m-icon-like"></i><h4>赞</h4></div><aside><i class="f-more">...</i></aside></footer></div></div></div></div>
    </router-view>
    <mv-modal></mv-modal>
  </div>
    <script defer src="https://static.geetest.com/v4/gt4.js"></script>
  <script>
    var config = {
      env: 'prod',
      version: 'v2.12.13',
      login: [][0],
      st: 'bf04ab',
      uid: '',
      pageConfig: [null][0] || {},
      preferQuickapp: '0',
      wm: ''
    }
  var $render_data = [{
    "hotScheme": "https://m.weibo.cn/p/index?containerid=106003type%3D25%26t%3D3%26disable_hot%3D1%26filter_type%3Drealtimehot&luicode=20000061&lfid=****************",
    "appScheme": "https://m.weibo.cn?luicode=20000061&lfid=****************",
    "callUinversalLink": false,
    "callWeibo": false,
    "schemeOrigin": false,
    "appLink": "sinaweibo://detail?mblogid=****************&luicode=20000061&lfid=****************",
    "xianzhi_scheme": "xianzhi://mblogshow?mid=****************",
    "third_scheme": "sinaweibo://detail?mblogid=****************&luicode=20000061&lfid=****************",
    "status": {
        "visible": {
            "type": 0,
            "list_id": 0
        },
        "created_at": "Sun Aug 10 11:38:20 +0800 2025",
        "id": "****************",
        "mid": "****************",
        "can_edit": false,
        "text": "混合123466 <a  href=\"http://weibo.com/p/1001018008636010000000000\" data-hide=\"\"><span class='url-icon'><img style='width: 1rem;height: 1rem' src='https://h5.sinaimg.cn/upload/2015/09/25/3/timeline_card_small_location_default.png'></span><span class=\"surl-text\">南昌</span></a> ",
        "textLength": 30,
        "source": "iPhone客户端",
        "favorited": false,
        "pic_ids": [
            "006J0BqZgy1i48ltwj1q0j30u0140wqx",
            "006J0BqZgy1i48ltthnicj30u01sy40b"
        ],
        "thumbnail_pic": "https://wx3.sinaimg.cn/thumbnail/006J0BqZgy1i48ltwj1q0j30u0140wqx.jpg",
        "bmiddle_pic": "http://wx3.sinaimg.cn/bmiddle/006J0BqZgy1i48ltwj1q0j30u0140wqx.jpg",
        "original_pic": "https://wx3.sinaimg.cn/large/006J0BqZgy1i48ltwj1q0j30u0140wqx.jpg",
        "is_paid": false,
        "mblog_vip_type": 0,
        "user": {
            "id": 6161876013,
            "screen_name": "用户6161876013",
            "profile_image_url": "https://tvax1.sinaimg.cn/default/images/default_avatar_male_180.gif?KID=imgbed,tva&Expires=1755408790&ssig=uwq1RTY2VQ",
            "profile_url": "https://m.weibo.cn/u/6161876013?luicode=20000061&lfid=****************",
            "close_blue_v": false,
            "description": "",
            "follow_me": false,
            "following": false,
            "follow_count": 0,
            "followers_count": "1",
            "cover_image_phone": "https://tva1.sinaimg.cn/crop.0.0.640.640.640/549d0121tw1egm1kjly3jj20hs0hsq4f.jpg",
            "avatar_hd": "https://ss1.sinaimg.cn/orj480/default_avatar_male_180&690",
            "badge": {
                "user_name_certificate": 1
            },
            "statuses_count": 2,
            "verified": false,
            "verified_type": -1,
            "gender": "m",
            "mbtype": 0,
            "svip": 0,
            "urank": 0,
            "mbrank": 0,
            "followers_count_str": "1",
            "verified_reason": "",
            "like": false,
            "like_me": false,
            "special_follow": false,
            "user_token": "a0.MTg1ZGEwZWU0OWVhNGQyY_eNQtJ5BGsir0BGPsEIUsDBSv-y3MbCQIZkDLodaXzV"
        },
        "reposts_count": 0,
        "comments_count": 0,
        "reprint_cmt_count": 0,
        "attitudes_count": 0,
        "mixed_count": 0,
        "pending_approval_count": 0,
        "isLongText": false,
        "show_mlevel": 0,
        "mix_media_ids": [
            "http://t.cn/A6syHQgS",
            "006J0BqZgy1i48ltwj1q0j30u0140wqx",
            "006J0BqZgy1i48ltthnicj30u01sy40b"
        ],
        "darwin_tags": [],
        "ad_marked": false,
        "mblogtype": 0,
        "item_category": "status",
        "rid": "0_0_0_4866582423261759101_0_0_0",
        "number_display_strategy": {
            "apply_scenario_flag": 19,
            "display_text_min_number": 1000000,
            "display_text": "100万+"
        },
        "comment_guide_ext": "guide_type:-1|source:1",
        "content_auth": 0,
        "is_show_mixed": false,
        "safe_tags": 524288,
        "comment_manage_info": {
            "comment_permission_type": -1,
            "approval_comment_type": 0,
            "comment_sort_type": 0
        },
        "pic_num": 2,
        "fid": 5198058110845008,
        "mlevel": 0,
        "region_name": "发布于 江西",
        "region_opt": 1,
        "detail_bottom_bar": 0,
        "is_all_video": false,
        "page_info": {
            "type": "video",
            "object_type": 11,
            "url_ori": "http://t.cn/A6syHQgS",
            "page_pic": {
                "width": "480",
                "pid": "006J0BqZly1i48ludodw8j30f40qo75a",
                "source": "21",
                "is_self_cover": "0",
                "type": "0",
                "url": "https://wx2.sinaimg.cn/orj480/006J0BqZly1i48ludodw8j30f40qo75a.jpg",
                "height": "847"
            },
            "page_url": "https://video.weibo.com/show?fid=1034%3A5198058074603627&luicode=20000061&lfid=****************",
            "object_id": "1034:5198058074603627",
            "page_title": "用户6161876013的微博视频",
            "title": "",
            "content1": "用户6161876013的微博视频",
            "content2": "混合123466 http://t.cn/R2WxjvI",
            "video_orientation": "vertical",
            "play_count": "13次播放",
            "media_info": {
                "stream_url": "https://f.video.weibocdn.com/o0/M1T8slrwlx08quTZaZ5m010412001dC90E010.mp4?label=mp4_ld&template=360x632.24.0&ori=0&ps=1BThihd3VLAY5R&Expires=1755401590&ssig=ftgK%2FD5G9e&KID=unistore,video",
                "stream_url_hd": "https://f.video.weibocdn.com/o0/Y6dbjC4tlx08quTZgsCc010412002i4y0E010.mp4?label=mp4_hd&template=540x952.24.0&ori=0&ps=1BThihd3VLAY5R&Expires=1755401590&ssig=7n%2FNVE0uvb&KID=unistore,video",
                "duration": 4.78300000000000036237679523765109479427337646484375
            },
            "urls": {
                "mp4_720p_mp4": "https://f.video.weibocdn.com/o0/2ieoWspXlx08quTZeF7G010412002p2M0E010.mp4?label=mp4_720p&template=544x960.24.0&ori=0&ps=1BThihd3VLAY5R&Expires=1755401590&ssig=wpRvlKpk6t&KID=unistore,video",
                "mp4_ld_mp4": "https://f.video.weibocdn.com/o0/M1T8slrwlx08quTZaZ5m010412001dC90E010.mp4?label=mp4_ld&template=360x632.24.0&ori=0&ps=1BThihd3VLAY5R&Expires=1755401590&ssig=ftgK%2FD5G9e&KID=unistore,video",
                "mp4_hd_mp4": "https://f.video.weibocdn.com/o0/Y6dbjC4tlx08quTZgsCc010412002i4y0E010.mp4?label=mp4_hd&template=540x952.24.0&ori=0&ps=1BThihd3VLAY5R&Expires=1755401590&ssig=7n%2FNVE0uvb&KID=unistore,video"
            }
        },
        "pics": [
            {
                "pid": "006J0BqZly1i48lue40goj30f40qo75a",
                "url": "https://wx4.sinaimg.cn/orj360/006J0BqZly1i48lue40goj30f40qo75a.jpg",
                "size": "orj360",
                "geo": {
                    "width": 360,
                    "height": 635,
                    "croped": false
                },
                "large": {
                    "size": "large",
                    "url": "https://wx4.sinaimg.cn/large/006J0BqZly1i48lue40goj30f40qo75a.jpg",
                    "geo": {
                        "width": "544",
                        "height": "960",
                        "croped": false
                    }
                },
                "duration": 4,
                "type": "video",
                "videoSrc": "https://f.video.weibocdn.com/o0/2ieoWspXlx08quTZeF7G010412002p2M0E010.mp4?label=mp4_720p&template=544x960.24.0&ori=0&ps=1BThihd3VLAY5R&Expires=1755401590&ssig=wpRvlKpk6t&KID=unistore,video"
            },
            {
                "pid": "006J0BqZgy1i48ltwj1q0j30u0140wqx",
                "url": "https://wx3.sinaimg.cn/orj360/006J0BqZgy1i48ltwj1q0j30u0140wqx.jpg",
                "size": "orj360",
                "geo": {
                    "width": 360,
                    "height": 480,
                    "croped": false
                },
                "large": {
                    "size": "large",
                    "url": "https://wx3.sinaimg.cn/large/006J0BqZgy1i48ltwj1q0j30u0140wqx.jpg",
                    "geo": {
                        "width": "1080",
                        "height": "1440",
                        "croped": false
                    }
                },
                "videoSrc": "https://video.weibo.com/media/play?livephoto=https%3A%2F%2Flivephoto.us.sinaimg.cn%2F003iDI2tgx08quTY0xSg0f0f01005dVq0k01.mov",
                "type": "livephoto"
            },
            {
                "pid": "006J0BqZgy1i48ltthnicj30u01sy40b",
                "url": "https://wx1.sinaimg.cn/orj360/006J0BqZgy1i48ltthnicj30u01sy40b.jpg",
                "size": "orj360",
                "geo": {
                    "width": 360,
                    "height": 779,
                    "croped": false
                },
                "large": {
                    "size": "large",
                    "url": "https://wx1.sinaimg.cn/large/006J0BqZgy1i48ltthnicj30u01sy40b.jpg",
                    "geo": {
                        "width": "1080",
                        "height": "2338",
                        "croped": false
                    }
                }
            }
        ],
        "live_photo": [
            "https://video.weibo.com/media/play?livephoto=https%3A%2F%2Flivephoto.us.sinaimg.cn%2F003iDI2tgx08quTY0xSg0f0f01005dVq0k01.mov"
        ],
        "bid": "PF8WirgIV",
        "buttons": [
            {
                "type": "follow",
                "name": "关注",
                "sub_type": 0,
                "params": {
                    "uid": 6161876013
                }
            }
        ],
        "status_title": "混合123466 南昌 ",
        "ok": 1
    },
    "call": 1
}][0] || {};
  var __wb_performance_data={v:"v8",m:"mainsite",pwa:1,sw:0};
  </script>
  <script src="https://h5.sinaimg.cn/upload/1005/16/2017/11/30/wbp.js" id="__wb_performance_log" data-rate="0.1"></script>
<script src="//h5.sinaimg.cn/m/weibo-lite/js/vendor.7fe5c79c.js"></script><script src="//h5.sinaimg.cn/m/weibo-lite/js/app.82cbb466.js"></script></body>

</html>
