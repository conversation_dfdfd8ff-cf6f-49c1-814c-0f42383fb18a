<template>
  <view class="container">
    <!-- 顶部信息 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px', height: customNavHeight + 'px' }">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <view class="back-btn-inner">
            <text class="back-icon">‹</text>
            <text class="back-text">返回</text>
          </view>
        </view>
        <text class="title">处理结果</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 图片选择器 -->
    <image-selector
      :show="showImageSelector"
      :images="selectorImages"
      @confirm="onImageSelectorConfirm"
      @cancel="onImageSelectorCancel"
    />

    <!-- Live Photo 选择器 -->
    <live-photo-selector
      :show="showLivePhotoSelector"
      :videoUrls="livePhotoSelectorVideos"
      :imageUrls="livePhotoSelectorImages"
      :livePhotoVideos="livePhotoSelectorLiveVideos"
      @confirm="onLivePhotoSelectorConfirm"
      @cancel="onLivePhotoSelectorCancel"
    />

    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: customNavHeight + 'px' }">
      <!-- 视频信息 -->
      <view class="video-info">
        <text class="video-title">{{ resultData.title }}</text>
        
        <!-- 小红书正文内容 - 直接跟在标题后面 -->
              <text v-if="resultData.content && resultData.content.trim()"
            class="video-content">{{ resultData.content }}</text>
        
        <text class="video-author">作者：{{ resultData.author }}</text>
        <text class="video-source">来源：{{ resultData.source }}</text>
      </view>

    <!-- 视频播放区域 -->
    <view class="video-section">
      <view class="video-container">
        <!-- 长视频警告（平台无关） -->
        <view class="long-video-warning" v-if="resultData.processedData && resultData.processedData.isLongVideo">
          <text class="warning-icon">⚠️</text>
          <text class="warning-text">长视频文件较大，建议在WiFi环境下播放和保存</text>
        </view>

        <!-- 处理中状态 -->
        <view v-if="resultData.processedData && resultData.processedData.isProcessing" class="processing-placeholder">
          <view class="processing-content">
            <view class="loading-spinner"></view>
            <text class="processing-text">正在后台处理视频...</text>
            <text class="processing-tip">请稍候，处理完成后将自动播放</text>
          </view>
        </view>

        <!-- 图文内容显示 -->
        <view v-else-if="resultData.type === 'image'" class="image-content">
          <view class="main-image-container">
            <!-- 图片加载状态 -->
            <view v-if="imageLoading" class="image-loading-placeholder">
              <view class="loading-spinner-small"></view>
              <text class="loading-text-small">加载中...</text>
            </view>

            <image
              :src="getCurrentImageUrl()"
              mode="aspectFit"
              class="main-image"
              :class="{ 'image-hidden': imageLoading }"
              @error="onMainImageError"
              @load="onMainImageLoad"
              @click="previewCurrentImage"
            ></image>

            <!-- 主图上的Live Photo播放按钮 -->
            <view v-if="hasLivePhotoForImage(currentImageIndex)"
                  class="main-live-photo-btn"
                  @click="playLivePhotoForImage(currentImageIndex)">
              <view class="main-play-icon">▶</view>
              <text class="main-live-text">LIVE PHOTO</text>
            </view>

            <!-- 主图上的复制链接按钮 (仅图文内容且有图片链接时显示，微博平台除外) -->
            <view v-if="resultData.type === 'image' && resultData.platform !== 'weibo' && resultData.processedData && 
                        (resultData.processedData.isUrl || (resultData.processedData.imageUrls && resultData.processedData.imageUrls.length > 0))"
                  class="main-copy-link-btn"
                  @click="copyCurrentImageUrl">
              <view class="main-copy-icon">🔗</view>
              <text class="main-copy-text">复制链接</text>
            </view>

            <!-- 图片信息 -->
            <view class="main-image-info">
              <text class="image-counter">{{ currentImageIndex + 1 }} / {{ resultData.processedData.imageUrls?.length || 1 }}</text>
            </view>
          </view>

          <!-- 多图显示 -->
          <view v-if="resultData.processedData.imageUrls && resultData.processedData.imageUrls.length > 1"
                class="image-gallery">
            <text class="gallery-title">
              共{{ resultData.processedData.imageUrls.length }}张图片
              <text v-if="livePhotoCount > 0"
                    class="live-photo-count-inline">
                ({{ livePhotoCount }}个Live Photo)
              </text>
            </text>
            <scroll-view scroll-x class="gallery-scroll">
              <view class="gallery-item" v-for="(imageUrl, index) in resultData.processedData.imageUrls" :key="index">
                <view class="image-container" :class="{ 'active': currentImageIndex === index }">
                  <image
                    :src="imageUrl"
                    mode="aspectFill"
                    class="gallery-image"
                    @click="selectImage(index)"
                  ></image>

                  <!-- Live Photo 播放按钮 -->
                  <view v-if="hasLivePhotoForImage(index)"
                        class="live-photo-play-btn"
                        @click.stop="playLivePhotoForImage(index)">
                    <view class="play-icon-small">▶</view>
                    <text class="live-text">LIVE</text>
                  </view>

                  <!-- 图片序号 -->
                  <view class="image-index">{{ index + 1 }}</view>

                  <!-- 选中状态指示器 -->
                  <view v-if="currentImageIndex === index" class="selected-indicator">✓</view>
                </view>
              </view>
            </scroll-view>
          </view>

          <!-- 隐藏的Live Photo视频播放器 -->
          <view v-if="showLivePhotoPlayer" class="live-photo-player-modal" @click="closeLivePhotoPlayer">
            <view class="live-photo-player-container" @click.stop>
              <view class="live-photo-player-header">
                <text class="live-photo-player-title">Live Photo {{ currentLivePhotoIndex + 1 }}</text>
                <view class="live-photo-player-close" @click="closeLivePhotoPlayer">✕</view>
              </view>

              <video
                id="livePhotoPlayerVideo"
                :src="currentLivePhotoUrl"
                class="live-photo-player-video"
                :controls="true"
                :autoplay="true"
                :show-fullscreen-btn="true"
                :show-play-btn="true"
                :show-center-play-btn="true"
                @ended="onLivePhotoPlayerEnded"
              ></video>

              <view class="live-photo-player-actions">
                <button class="live-photo-player-btn" @click="downloadCurrentLivePhoto">
                  <text class="btn-icon">💾</text>
                  <text class="btn-text">保存到相册</text>
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 正常视频播放 -->
        <video
          v-else
          :src="getVideoSrc(resultData.processedData)"
          controls
          class="main-video"
          @error="onVideoError"
          @loadstart="onVideoLoadStart"
          @canplay="onVideoCanPlay"
          show-center-play-btn
          show-fullscreen-btn
          show-play-btn
        ></video>

        <view class="video-status" v-if="videoStatus">
          <text class="status-text">{{ videoStatus }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <!-- 第一行：主要操作 -->
      <view class="action-row">
        <button class="action-btn primary-btn full-width" @click="saveToAlbum">
          <text class="btn-icon">💾</text>
          <text class="btn-text">保存到相册</text>
        </button>
      </view>

      <!-- 第二行：辅助操作 -->
      <view class="action-row">
        <button class="action-btn secondary-btn half-width" @click="getCopyText">
          <text class="btn-icon">📝</text>
          <text class="btn-text">获取文案</text>
        </button>

        <button class="action-btn secondary-btn half-width" @click="getCover">
          <text class="btn-icon">🖼️</text>
          <text class="btn-text">获取封面</text>
        </button>
      </view>

      <!-- 第三行：链接操作 (多图时隐藏，因为主图上有复制链接按钮) -->
      <view v-if="shouldShowCopyLink" class="action-row">
        <button class="action-btn secondary-btn full-width" @click="copyVideoUrl">
          <text class="btn-icon">🔗</text>
          <text class="btn-text">复制链接</text>
        </button>
      </view>
    </view>

    <!-- 处理信息 -->
    <view class="process-info">
      <!-- 只在纯视频内容时显示文件大小 -->
      <view v-if="resultData.type === 'video'" class="info-item">
        <text class="info-label">文件大小：</text>
        <text class="info-value">{{ getOptimizedDataSize(resultData.processedData) }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">内容信息：</text>
        <text class="info-value">{{ getOptimizedNote(resultData) }}</text>
      </view>
    </view>

    <!-- 免责声明 -->
    <view class="disclaimer">
      <text class="disclaimer-title">⚠️ 重要提醒</text>
      <text class="disclaimer-text">
        1. 本工具仅供个人学习交流使用
        2. 请尊重原创作者的版权
        3. 禁止用于任何商业或侵权用途
        4. 视频版权归原作者所有
      </text>
    </view>
    </view> <!-- 关闭page-content -->

    <!-- 自定义Loading组件 -->
    <view v-if="customLoading.show" class="custom-loading-mask">
      <view class="custom-loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ customLoading.title }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import ImageSelector from '@/components/image-selector/image-selector-simple.vue'
import LivePhotoSelector from '@/components/live-photo-selector/live-photo-selector.vue'

export default {
  components: {
    ImageSelector,
    LivePhotoSelector
  },

  data() {
    return {
      resultData: {},
      videoStatus: '',
      statusBarHeight: 0, // 状态栏高度
      customNavHeight: 0, // 自定义导航栏高度
      currentDownloadTask: null, // 当前下载任务
      isPageActive: true, // 页面是否活跃
      cachedFileSize: 0, // 缓存的文件大小
      showImageSelector: false, // 显示图片选择器
      selectorImages: [], // 选择器中的图片列表
      currentImageSelectorResolve: null, // 当前图片选择器的resolve函数
      // Live Photo 选择器相关
      showLivePhotoSelector: false,
      livePhotoSelectorVideos: [],
      livePhotoSelectorImages: [],
      livePhotoSelectorLiveVideos: [],
      currentLivePhotoSelectorResolve: null,
      customLoading: {
        show: false,
        title: ''
      },
      // Live Photo 相关
      showLivePhotoPlayer: false,
      currentLivePhotoIndex: 0,
      currentLivePhotoUrl: '',
      // 图片预览相关
      currentImageIndex: 0,
      imageLoading: false,
      preloadedImages: new Set() // 已预加载的图片集合
    }
  },
  
  computed: {
    // 计算实际Live Photo数量
    livePhotoCount() {
      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      if (!livePhotoVideos) return 0;
      return livePhotoVideos.filter(video => video && video !== null).length;
    },
    
    // 判断是否应该显示复制链接按钮
    shouldShowCopyLink() {
      // 如果是图文内容，不显示复制链接按钮（因为图片上已经有复制按钮了）
      if (this.resultData.type === 'image') {
        return false;
      }
      
      // 如果有可用的URL数据，显示复制链接按钮
      if (this.resultData.processedData && this.resultData.processedData.isUrl) {
        // 额外检查：如果是微博链接，不显示复制按钮（因为无法直接访问）
        const videoUrl = this.resultData.processedData.data || this.resultData.processedData.videoUrl;
        if (videoUrl && typeof videoUrl === 'string' && (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn'))) {
          return false;
        }
        return true;
      }
      
      return false;
    }
  },
  
  onLoad(options) {
    // 获取系统信息，适配刘海屏
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
    this.customNavHeight = this.statusBarHeight + 44 // 44是导航栏内容高度

    // 从页面参数或全局数据中获取结果
    const resultStr = options.result || uni.getStorageSync('temp_result')
    if (resultStr) {
      try {
        this.resultData = JSON.parse(resultStr)

        // 如果是纯视频内容且为URL格式，尝试获取文件大小
        if (this.resultData.type === 'video' && this.resultData.processedData && this.resultData.processedData.isUrl) {
          this.loadFileSize()
        }

        // 如果是图文内容，开始预加载图片
        if (this.resultData.type === 'image' && this.resultData.processedData?.imageUrls?.length > 1) {
          this.$nextTick(() => {
            this.preloadAdjacentImages(0) // 预加载第一张图片周围的图片
          })
        }
      } catch (error) {
        console.error('解析结果数据失败:', error)
        this.goBack()
      }
    } else {
      uni.showToast({
        title: '没有找到结果数据',
        icon: 'none'
      })
      this.goBack()
    }
  },

  // 页面隐藏时中断下载
  onHide() {
    this.isPageActive = false
    this.cancelCurrentDownload()
  },

  // 页面卸载时中断下载
  onUnload() {
    this.isPageActive = false
    this.cancelCurrentDownload()
  },

  // 页面显示时恢复状态
  onShow() {
    this.isPageActive = true
  },

  methods: {
    // 显示自定义loading
    showCustomLoading(title) {
      this.customLoading.show = true
      this.customLoading.title = title
    },

    // 隐藏自定义loading
    hideCustomLoading() {
      this.customLoading.show = false
      this.customLoading.title = ''
    },

    // 图片选择器确认回调
    onImageSelectorConfirm(selectedIndexes) {
      this.showImageSelector = false
      if (this.currentImageSelectorResolve) {
        this.currentImageSelectorResolve(selectedIndexes)
        this.currentImageSelectorResolve = null
      }
    },

    // 图片选择器取消回调
    onImageSelectorCancel() {
      this.showImageSelector = false
      if (this.currentImageSelectorResolve) {
        this.currentImageSelectorResolve(null)
        this.currentImageSelectorResolve = null
      }
    },

    // Live Photo 选择器确认回调
    onLivePhotoSelectorConfirm(selectedIndexes) {
      this.showLivePhotoSelector = false
      if (this.currentLivePhotoSelectorResolve) {
        this.currentLivePhotoSelectorResolve(selectedIndexes)
        this.currentLivePhotoSelectorResolve = null
      }
    },

    // Live Photo 选择器取消回调
    onLivePhotoSelectorCancel() {
      this.showLivePhotoSelector = false
      if (this.currentLivePhotoSelectorResolve) {
        this.currentLivePhotoSelectorResolve(null)
        this.currentLivePhotoSelectorResolve = null
      }
    },

    // 取消当前下载任务
    cancelCurrentDownload() {
      if (this.currentDownloadTask) {
        try {
          this.currentDownloadTask.abort()
        } catch (error) {
          console.error('取消下载任务失败:', error)
        }
        this.currentDownloadTask = null
        this.hideCustomLoading()
      }
    },

    // 返回上一页
    goBack() {
      // 返回前先取消下载
      this.cancelCurrentDownload()
      uni.navigateBack()
    },
    
    // 获取视频源
    getVideoSrc(processedData) {
      if (processedData && processedData.data) {
        if (processedData.isUrl) {
          return processedData.data
        }
        if (processedData.data.startsWith('data:')) {
          return processedData.data
        }
        return `data:${processedData.type};base64,${processedData.data}`
      }
      return ''
    },
    
    // 获取优化的文件大小显示
    getOptimizedDataSize(processedData) {
      if (!processedData || !processedData.data) {
        return '未知大小'
      }

      if (processedData.isUrl) {
        // 对于URL格式，尝试从已缓存的大小信息获取
        if (this.cachedFileSize && this.cachedFileSize > 0) {
          return this.formatFileSize(this.cachedFileSize)
        }

        // 如果有时长信息，可以估算大小
        if (processedData.duration && processedData.duration > 0) {
          const estimatedSize = this.estimateVideoSize(processedData.duration, processedData.isLongVideo)
          return `约 ${this.formatFileSize(estimatedSize)}`
        }

        return '获取中...'
      }

      // Base64格式，计算实际大小
      const base64Data = processedData.data.replace(/^data:[^;]+;base64,/, '')
      const sizeInBytes = (base64Data.length * 3) / 4
      return this.formatFileSize(sizeInBytes)
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes < 1024) {
        return `${Math.round(bytes)} B`
      } else if (bytes < 1024 * 1024) {
        return `${Math.round(bytes / 1024)} KB`
      } else if (bytes < 1024 * 1024 * 1024) {
        return `${Math.round(bytes / (1024 * 1024) * 10) / 10} MB`
      } else {
        return `${Math.round(bytes / (1024 * 1024 * 1024) * 10) / 10} GB`
      }
    },

    // 估算视频大小
    estimateVideoSize(durationSeconds, isLongVideo) {
      // 根据视频时长和质量估算大小
      // 短视频通常码率较高，长视频码率较低
      const bitrate = isLongVideo ? 1500000 : 3000000 // 1.5Mbps 或 3Mbps
      return Math.round((durationSeconds * bitrate) / 8) // 转换为字节
    },

    // 获取优化的处理说明
    getOptimizedNote(resultData) {
      if (!resultData) return '未知'

      const type = resultData.type
      const processedData = resultData.processedData

      if (type === 'image') {
        // 图文内容
        const imageCount = processedData.imageUrls ? processedData.imageUrls.length : 1
        return `高清图集 (${imageCount}张)`
      } else if (type === 'video') {
        // 视频内容
        let description = ''

        // 判断是否无水印
        if (resultData.note && resultData.note.includes('无水印')) {
          description = '无水印视频'
        } else {
          description = '视频内容'
        }

        // 添加时长信息
        if (processedData.duration && processedData.duration > 0) {
          const minutes = Math.floor(processedData.duration / 60)
          const seconds = processedData.duration % 60
          if (minutes > 0) {
            description += ` (${minutes}分${seconds}秒)`
          } else {
            description += ` (${seconds}秒)`
          }
        }

        // 添加长视频标识
        if (processedData.isLongVideo) {
          description += ' · 长视频'
        }

        return description
      }

      return resultData.note || '处理完成'
    },
    
    // 视频事件处理
    onVideoLoadStart() {
      this.videoStatus = '正在加载视频...'
    },
    
    onVideoCanPlay() {
      this.videoStatus = '视频加载完成'
      setTimeout(() => {
        this.videoStatus = ''
      }, 2000)
    },
    
      onVideoError(e) {
      console.error('视频加载失败:', e)
      const isLongVideo = this.resultData.processedData?.isLongVideo || false
        
        if (isLongVideo) {
        this.videoStatus = '长视频播放失败，建议保存到相册后播放'

        if (this.isPageActive) {
          uni.showModal({
            title: '播放失败',
            content: '视频文件较大无法在线播放，建议保存到相册后观看或复制链接到浏览器下载',
            showCancel: true,
            cancelText: '复制链接',
            confirmText: '保存到相册',
            success: (res) => {
              if (res.confirm) {
                this.saveToAlbum()
              } else {
                // 复制视频链接作为备用方案
                this.copyVideoUrl()
              }
            }
          })
        }
        } else {
        this.videoStatus = '视频加载失败'
        if (this.isPageActive) {
          uni.showModal({
            title: '播放失败',
              content: '视频无法播放，建议保存到相册后观看或复制链接到浏览器下载',
            showCancel: false
          })
        }
      }
    },

    // 主图加载事件
    onMainImageLoad() {
      this.imageLoading = false

      // 将当前图片添加到预加载集合
      const currentImageUrl = this.getCurrentImageUrl()
      if (currentImageUrl) {
        this.preloadedImages.add(currentImageUrl)
      }
    },

    onMainImageError(e) {
      // 静默处理主图加载失败
      this.imageLoading = false
      if (this.isPageActive) {
        uni.showToast({
          title: '图片加载失败',
          icon: 'none'
        })
      }
    },

    // 预览当前图片（点击大图时调用）
    previewCurrentImage() {
      const imageUrls = this.resultData.processedData?.imageUrls || []
      if (imageUrls.length === 0) {
        return
      }

      // 使用当前图片索引而不是URL，这样更稳定

      uni.previewImage({
        current: this.currentImageIndex, // 使用索引
        urls: imageUrls,
        success: () => {
        },
        fail: (error) => {
          console.error('预览失败:', error)
          // 如果索引方式失败，尝试URL方式
          const currentImageUrl = this.getCurrentImageUrl()
          uni.previewImage({
            current: currentImageUrl,
            urls: imageUrls
          })
        }
      })
    },

    // 保存到相册
    async saveToAlbum() {
      if (!this.resultData.processedData) {
        if (this.isPageActive) {
          uni.showToast({
            title: '没有可保存的内容',
            icon: 'none'
          })
        }
        return
      }

      // 检查相册权限
      try {
        await this.checkAlbumPermission()
      } catch (error) {
        console.error('相册权限检查失败:', error)
        if (this.isPageActive) {
          uni.showModal({
            title: '需要相册权限',
            content: '保存视频需要访问您的相册，请在设置中开启相册权限',
            showCancel: false
          })
        }
        return
      }

      try {
        // 优先根据内容类型判断保存逻辑
        if (this.resultData.type?.includes('video')) {
          // 直接执行视频保存逻辑（不走图文逻辑）
        } else if (this.resultData.type === 'image') {
          await this.saveImageContent()
          return
        }

        this.showCustomLoading('开始下载，请耐心等待...')

        if (this.resultData.processedData.isUrl) {
          // URL格式，先检查文件大小


          // 检查文件大小和视频长度
          const fileSize = await this.checkVideoSize(this.resultData.processedData.data)
          const isLongVideo = this.resultData.processedData.isLongVideo
          const duration = this.resultData.processedData.duration || 0

          if (fileSize > 30 * 1024 * 1024 || isLongVideo) {
            let warningContent = ''
            if (isLongVideo) {
              const minutes = Math.floor(duration / 60)
              const seconds = duration % 60
              warningContent = `视频时长${minutes}分${seconds}秒，文件较大`
            }
            if (fileSize > 0) {
              warningContent += `（约${Math.round(fileSize / 1024 / 1024)}MB）`
            }
            warningContent += '，建议在WiFi环境下下载，是否继续？'

            const confirmResult = await new Promise((resolve) => {
              uni.showModal({
                title: '长视频提醒',
                content: warningContent,
                confirmText: '继续下载',
                cancelText: '取消',
                success: (res) => resolve(res.confirm)
              })
            })

            if (!confirmResult) {
              this.hideCustomLoading()
              // 用户主动取消，不显示错误提示
              return
            }
          }



          let downloadResult
          try {
            downloadResult = await this.downloadVideo(this.resultData.processedData.data)
          } catch (error) {
            // 检查是否需要重新解析
            if (error.message.startsWith('NEED_REPARSE:')) {
              try {
                // 静默重新解析，不显示任何提示
                await this.reParseVideo()
                // 重新解析成功后，递归调用保存方法
                this.saveToAlbum()
                return
              } catch (reparseError) {
                console.error('自动重新解析失败:', reparseError)

                // 如果重新解析失败，尝试直接使用当前URL下载（可能是临时网络问题）
                try {
                  // 移除NEED_REPARSE前缀，直接尝试下载
                  const originalUrl = this.resultData.processedData.data
                  const directDownloadResult = await this.downloadVideo(originalUrl, 0, true)

                  // 如果直接下载成功，继续保存流程
                  downloadResult = directDownloadResult
                } catch (directDownloadError) {
                  console.error('直接下载也失败:', directDownloadError)
                  // 如果直接下载也失败，抛出原始错误
                  throw error
                }
              }
            } else {
              throw error
            }
          }

          // 保存到相册
          await new Promise((resolve, reject) => {
            uni.saveVideoToPhotosAlbum({
              filePath: downloadResult,
              success: (res) => {
                resolve(res)
              },
              fail: (error) => {
                console.error('保存到相册失败:', error)

                // 检查是否是开发工具的问题
                if (error.errMsg && (error.errMsg.includes('fail') || error.errMsg.includes('ENOENT'))) {
                  reject(new Error('保存失败，这可能是微信开发工具的问题。真机测试通常正常，或者可以复制链接到浏览器下载'))
                } else {
                  reject(new Error('保存到相册失败，请检查相册权限'))
                }
              }
            })
          })

        } else {
          // Base64格式，提示手动保存
          this.hideCustomLoading()
          if (this.isPageActive) {
            uni.showModal({
              title: '保存提示',
              content: '请长按视频选择"保存视频"来手动保存到相册',
              showCancel: false
            })
          }
          return
        }

        this.hideCustomLoading()

        // 只有在页面还活跃时才显示成功提示
        if (this.isPageActive) {
          uni.showModal({
            title: '保存成功',
            content: '视频已成功保存到您的相册中',
            showCancel: false,
            confirmText: '知道了'
          })
        }

      } catch (error) {
        console.error('保存过程出错:', error)
        this.hideCustomLoading()

        // 清理下载任务引用
        this.currentDownloadTask = null

        // 如果页面已离开或错误信息包含"页面已离开"，不显示错误提示
        if (!this.isPageActive || (error.message && error.message.includes('页面已离开'))) {
          console.log('页面已离开，不显示错误提示')
          return
        }

        let errorMessage = '保存失败'
        let suggestions = []

        if (error.message.includes('权限')) {
          errorMessage = '相册权限被拒绝'
          suggestions.push('请在手机设置中开启相册权限')
        } else if (error.message.includes('失效') || error.message.includes('无法访问') || error.message.includes('不是视频文件')) {
          errorMessage = '视频链接已失效'
          suggestions.push('尝试重新解析获取新链接')
          suggestions.push('或复制链接到浏览器下载')
        } else if (error.message.includes('超时') || error.message.includes('网络')) {
          errorMessage = '网络连接超时'
          suggestions.push('请检查网络连接')
          suggestions.push('建议在WiFi环境下重试')
        } else if (error.message.includes('下载')) {
          errorMessage = '视频下载失败'
          suggestions.push('请检查网络连接')
          suggestions.push('可能是视频文件较大')
        } else if (error.message.includes('文件')) {
          errorMessage = '文件处理失败'
          suggestions.push('可能是存储空间不足')
        }

        const content = suggestions.length > 0
          ? `${errorMessage}\n\n建议：\n${suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')}\n\n也可复制链接到浏览器下载`
          : `${errorMessage}，建议检查网络连接或在WiFi环境下重试，也可复制链接到浏览器下载`

        // 简化错误处理，不再提供手动重新解析选项
        uni.showModal({
          title: '保存失败',
          content: content,
          showCancel: true,
          cancelText: '复制链接',
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              this.saveToAlbum() // 重试
            } else {
              // 复制视频链接作为备用方案
              this.copyVideoUrl()
            }
          }
        })
      }
    },

    // 保存图文内容
    async saveImageContent() {
      try {

        const imageUrls = this.resultData.processedData.imageUrls || [this.resultData.processedData.data]
        // 同时检查videoUrls和livePhotoVideos字段，确保兼容性
        const videoUrls = this.resultData.processedData.videoUrls || this.resultData.processedData.livePhotoVideos || []
        const totalImages = imageUrls.length
        const totalVideos = videoUrls.length



        if (totalImages === 0 && totalVideos === 0) {
          uni.showToast({
            title: '没有可保存的内容',
            icon: 'none'
          })
          return
        }

        // 如果有Live Photo视频，询问用户要保存什么
        if (totalVideos > 0) {
          const choice = await this.showSaveChoiceDialog(totalImages, totalVideos)
          if (choice === 'cancel') {
            return
          } else if (choice === 'videos') {
            // 选择要保存的Live Photo视频
            await this.selectAndSaveLivePhotoVideos(videoUrls)
            return
          } else if (choice === 'both') {
            // 保存图片和视频
            await this.saveImagesAndVideos(imageUrls, videoUrls)
            return
          }
          // choice === 'images' 时继续执行下面的图片保存逻辑
        }

      // 多张图片时显示选择界面
      if (totalImages > 1) {
        const selectedIndexes = await this.showImageSelectorDialog(imageUrls)

        if (!selectedIndexes || selectedIndexes.length === 0) {
          return
        }

        // 保存选中的图片
        await this.saveSelectedImages(imageUrls, selectedIndexes)
        return
      }

      // 保存所有图片
      // 显示批量保存的加载遮罩
      this.showCustomLoading(`正在导出图片，请稍候...`)

      let successCount = 0
      let failCount = 0

      for (let i = 0; i < imageUrls.length; i++) {
        try {
          // 更新进度提示
          this.showCustomLoading(`正在导出第 ${i + 1}/${totalImages} 张图片`)
          await this.saveImageToAlbumSilent(imageUrls[i], i + 1)
          successCount++

          // 添加延迟，避免保存过快
          if (i < imageUrls.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        } catch (error) {
          console.error(`保存第${i + 1}张图片失败:`, error)
          failCount++
        }
      }

      console.log(`批量保存完成，成功: ${successCount}张，失败: ${failCount}张`)

      this.hideCustomLoading()

        // 显示保存结果
        if (successCount === totalImages) {
          uni.showModal({
            title: '保存成功',
            content: `已成功保存${successCount}张图片到您的相册中`,
            showCancel: false,
            confirmText: '知道了'
          })
        } else if (successCount > 0) {
          uni.showModal({
            title: '部分保存成功',
            content: `成功保存${successCount}张图片，${failCount}张失败`,
            showCancel: false
          })
        } else {
          uni.showModal({
            title: '保存失败',
            content: '所有图片保存失败，请检查网络连接和相册权限',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('保存图文内容过程中出错:', error)
        this.hideCustomLoading()

        // 忽略微信配置相关的错误
        if (error.message && error.message.includes('mp-weixin.oauth.weixin.appid')) {
  
          return
        }

        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 显示保存选择对话框
    async showSaveChoiceDialog(imageCount, videoCount) {
      return new Promise((resolve) => {
        const actions = []

        if (imageCount > 0) {
          actions.push(`保存图片 (${imageCount}张)`)
        }
        if (videoCount > 0) {
          actions.push(`保存Live Photo视频 (${videoCount}个)`)
        }
        if (imageCount > 0 && videoCount > 0) {
          actions.push(`保存全部 (${imageCount}张图片 + ${videoCount}个视频)`)
        }

        uni.showActionSheet({
          itemList: actions,
          success: (res) => {
            const index = res.tapIndex
            if (imageCount > 0 && videoCount > 0) {
              // 有图片和视频
              if (index === 0) resolve('images')
              else if (index === 1) resolve('videos')
              else if (index === 2) resolve('both')
            } else if (imageCount > 0) {
              // 只有图片
              resolve('images')
            } else {
              // 只有视频
              resolve('videos')
            }
          },
          fail: () => {
            resolve('cancel')
          }
        })
      })
    },

    // 选择并保存Live Photo视频
    async selectAndSaveLivePhotoVideos(videoUrls) {
      try {

        // 显示Live Photo选择界面
        const selectedIndexes = await this.showLivePhotoSelectorDialog(videoUrls)

        if (!selectedIndexes || selectedIndexes.length === 0) {
          return
        }

        // 获取选中的视频URL - 从livePhotoVideos数组中获取，而不是videoUrls
        const imageUrls = this.resultData.processedData?.imageUrls || []
        const livePhotoVideos = this.resultData.processedData?.livePhotoVideos || []
        
        // 根据选中的索引获取对应的视频URL
        const selectedVideoUrls = []
        const selectedVideoInfo = []
        
        for (let i = 0; i < selectedIndexes.length; i++) {
          const imageIndex = selectedIndexes[i]  // 这是图片在原数组中的索引
          const videoUrl = livePhotoVideos[imageIndex]  // 从livePhotoVideos数组中获取对应视频URL
          
          if (videoUrl && typeof videoUrl === 'string' && videoUrl.trim() !== '') {
            selectedVideoUrls.push(videoUrl)
            selectedVideoInfo.push({ url: videoUrl, index: imageIndex })
          } else {
            console.error(`第${imageIndex + 1}个Live Photo视频URL无效:`, videoUrl)
          }
        }



        // 保存选中的视频
        await this.saveLivePhotoVideos(selectedVideoUrls, selectedVideoInfo)

      } catch (error) {
        console.error('选择和保存Live Photo视频失败:', error)
        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 显示Live Photo选择对话框
    async showLivePhotoSelectorDialog(videoUrls) {
      return new Promise((resolve) => {

        // 传递正确的数据给选择器
        this.livePhotoSelectorVideos = videoUrls
        this.livePhotoSelectorImages = this.resultData.processedData?.imageUrls || []
        this.livePhotoSelectorLiveVideos = this.resultData.processedData?.livePhotoVideos || videoUrls || []
        this.showLivePhotoSelector = true
        this.currentLivePhotoSelectorResolve = resolve
      })
    },

    // 保存Live Photo视频
    async saveLivePhotoVideos(videoUrls, videoInfo = null) {
      try {
        let successCount = 0
        let failCount = 0

        // 显示批量下载的加载遮罩
        this.showCustomLoading(`正在导出 Live Photo 视频，请稍候...`)

        for (let i = 0; i < videoUrls.length; i++) {
          try {
            const videoUrl = videoUrls[i]
            const displayIndex = videoInfo ? videoInfo[i].index + 1 : i + 1

            console.log(`开始保存第${i + 1}个Live Photo视频 (Live Photo ${displayIndex}):`, videoUrl)
            
            // 检查videoUrl是否有效
            if (!videoUrl || typeof videoUrl !== 'string' || videoUrl.trim() === '') {
              console.error(`第${i + 1}个Live Photo视频URL无效:`, videoUrl)
              failCount++
              continue
            }

            // 更新进度提示
            this.showCustomLoading(`正在导出第 ${i + 1}/${videoUrls.length} 个 Live Photo 视频`)

            // 使用修复后的downloadLivePhoto方法，但不显示单独的加载提示
            await this.downloadLivePhotoSilent(videoUrl, videoInfo ? videoInfo[i].index : i)
            successCount++

            // 添加延迟，避免并发过多
            if (i < videoUrls.length - 1) {
              await new Promise(resolve => {
                setTimeout(() => {
                  resolve()
                }, 1000)
              })
            }
          } catch (error) {
            console.error(`保存第${i + 1}个Live Photo视频失败:`, error)
            failCount++
          }
        }

        // 隐藏加载遮罩
        this.hideCustomLoading()

        // 显示保存结果
        if (successCount === videoUrls.length) {
          uni.showModal({
            title: '保存成功',
            content: `已成功保存${successCount}个Live Photo视频到您的相册中`,
            showCancel: false
          })
        } else if (successCount > 0) {
          uni.showModal({
            title: '部分保存成功',
            content: `成功保存${successCount}个Live Photo视频，${failCount}个失败`,
            showCancel: false
          })
        } else {
          uni.showModal({
            title: '保存失败',
            content: '所有Live Photo视频保存失败，请检查网络连接和相册权限',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('保存Live Photo视频过程中出错:', error)
        this.hideCustomLoading()
        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 保存图片和视频
    async saveImagesAndVideos(imageUrls, videoUrls) {
      try {
        const totalItems = imageUrls.length + videoUrls.length
        let currentIndex = 0
        let successCount = 0
        let failCount = 0

        // 先保存图片
        for (let i = 0; i < imageUrls.length; i++) {
          try {
            currentIndex++
            this.showCustomLoading(`正在保存内容 ${currentIndex}/${totalItems} (图片 ${i + 1})`)
            await this.saveImageToAlbum(imageUrls[i], i + 1, imageUrls.length)
            successCount++

            if (currentIndex < totalItems) {
              await new Promise(resolve => setTimeout(resolve, 500))
            }
          } catch (error) {
            console.error(`保存第${i + 1}张图片失败:`, error)
            failCount++
          }
        }

        // 再保存视频
        for (let i = 0; i < videoUrls.length; i++) {
          try {
            currentIndex++
            this.showCustomLoading(`正在保存内容 ${currentIndex}/${totalItems} (Live Photo ${i + 1})`)

            await new Promise((resolve, reject) => {
              // 🔧 为微博Live Photo添加正确的请求头
              const headers = {}
              const videoUrl = videoUrls[i]
              if (videoUrl && (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn') || 
                  (videoUrl.includes('video.weibo.com') && videoUrl.includes('livephoto=')))) {
                headers['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                headers['Referer'] = 'https://weibo.com/'
                headers['Accept'] = '*/*'
                headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
                headers['Cache-Control'] = 'no-cache'
                headers['Pragma'] = 'no-cache'
              }

              const downloadTask = uni.downloadFile({
                url: videoUrls[i],
                filePath: `${wx.env.USER_DATA_PATH}/livephoto_${Date.now()}_${i}.mp4`,
                header: headers,
                timeout: 30000,
                success: (res) => {
                  if (res.statusCode === 200) {
                    const savedPath = res.filePath || res.tempFilePath
                    uni.saveVideoToPhotosAlbum({
                      filePath: savedPath,
                      success: () => {
                        successCount++
                        resolve()
                      },
                      fail: reject
                    })
                  } else {
                    reject(new Error(`下载失败，状态码: ${res.statusCode}`))
                  }
                },
                fail: reject
              })
            })

            if (currentIndex < totalItems) {
              console.log(`等待1秒后下载下一个Live Photo...`)
              await new Promise(resolve => {
                setTimeout(() => {
                  console.log(`延迟结束，准备下载下一个内容`)
                  resolve()
                }, 1000)
              })
            }
          } catch (error) {
            console.error(`保存第${i + 1}个Live Photo视频失败:`, error)
            failCount++
          }
        }

        this.hideCustomLoading()

        // 显示保存结果
        if (successCount === totalItems) {
          uni.showModal({
            title: '保存成功',
            content: `已成功保存${imageUrls.length}张图片和${videoUrls.length}个Live Photo视频到您的相册中`,
            showCancel: false
          })
        } else if (successCount > 0) {
          uni.showModal({
            title: '部分保存成功',
            content: `成功保存${successCount}项内容，${failCount}项失败`,
            showCancel: false
          })
        } else {
          uni.showModal({
            title: '保存失败',
            content: '所有内容保存失败，请检查网络连接和相册权限',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('保存图片和视频过程中出错:', error)
        this.hideCustomLoading()
        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 显示图片选择器对话框
    async showImageSelectorDialog(imageUrls) {
      return new Promise((resolve) => {
        // 如果只有一张图片，直接保存
        if (imageUrls.length === 1) {
          resolve([0])
          return
        }

        // 直接显示可视化选择器
        this.selectorImages = imageUrls
        this.showImageSelector = true
        this.currentImageSelectorResolve = resolve
      })
    },

    // 保存选中的图片
    async saveSelectedImages(imageUrls, selectedIndexes) {
      const totalSelected = selectedIndexes.length
      console.log(`开始保存选中的${totalSelected}张图片`)

      // 显示批量保存的加载遮罩
      this.showCustomLoading(`正在导出图片，请稍候...`)

      let successCount = 0
      let failCount = 0

      for (let i = 0; i < selectedIndexes.length; i++) {
        const imageIndex = selectedIndexes[i]
        const imageUrl = imageUrls[imageIndex]

        try {
          console.log(`开始保存第${imageIndex + 1}张图片:`, imageUrl)
          // 更新进度提示
          this.showCustomLoading(`正在导出第 ${i + 1}/${totalSelected} 张图片`)
          await this.saveImageToAlbumSilent(imageUrl, imageIndex + 1)
          successCount++
          console.log(`第${imageIndex + 1}张图片保存成功`)

          // 添加延迟，避免保存过快
          if (i < selectedIndexes.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        } catch (error) {
          console.error(`保存第${imageIndex + 1}张图片失败:`, error)
          failCount++
        }
      }

      console.log(`批量保存完成，成功: ${successCount}张，失败: ${failCount}张`)

      this.hideCustomLoading()

      // 显示保存结果
      if (successCount === totalSelected) {
        uni.showModal({
          title: '保存成功',
          content: `已成功保存${successCount}张图片到您的相册中`,
          showCancel: false,
          confirmText: '知道了'
        })
      } else if (successCount > 0) {
        uni.showModal({
          title: '部分保存成功',
          content: `成功保存${successCount}张图片，${failCount}张失败`,
          showCancel: false
        })
      } else {
        uni.showModal({
          title: '保存失败',
          content: '所有图片保存失败，请检查网络连接和相册权限',
          showCancel: false
        })
      }
    },

    // 静默保存单张图片到相册（用于批量保存，不显示单独的提示）
    async saveImageToAlbumSilent(imageUrl, current) {
      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: imageUrl,
          timeout: 30000,
          header: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            'Referer': 'https://www.douyin.com/'
          },
          success: (res) => {
            if (res.statusCode === 200 && res.tempFilePath) {
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  console.log(`第${current}张图片保存成功`)
                  resolve()
                },
                fail: (error) => {
                  console.error(`第${current}张图片保存到相册失败:`, error)
                  reject(new Error(`第${current}张图片保存失败`))
                }
              })
            } else {
              reject(new Error(`第${current}张图片下载失败，状态码: ${res.statusCode}`))
            }
          },
          fail: (error) => {
            console.error(`第${current}张图片下载失败:`, error)
            reject(new Error(`第${current}张图片下载失败`))
          }
        })
      })
    },

    // 保存单张图片到相册
    async saveImageToAlbum(imageUrl, current, total) {
      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: imageUrl,
          timeout: 30000,
          header: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            'Referer': 'https://www.douyin.com/'
          },
          success: (res) => {
            if (res.statusCode === 200 && res.tempFilePath) {
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  console.log(`第${current}张图片保存成功`)
                  resolve()
                },
                fail: (error) => {
                  console.error(`第${current}张图片保存到相册失败:`, error)
                  reject(new Error(`第${current}张图片保存失败`))
                }
              })
            } else {
              reject(new Error(`第${current}张图片下载失败，状态码: ${res.statusCode}`))
            }
          },
          fail: (error) => {
            console.error(`第${current}张图片下载失败:`, error)
            reject(new Error(`第${current}张图片下载失败`))
          }
        })
      })
    },

    // 重新解析视频（无UI版本，用于自动重新解析）
    async reParseVideo(retryCount = 0) {
      const maxRetries = 2 // 最多重试2次

      if (!this.resultData.originalUrl) {
        throw new Error('无法重新解析：缺少原始链接')
      }

      try {
        // 使用统一解析器重新解析
        const result = await uniCloud.callFunction({
          name: 'unified-parser',
          data: {
            link: this.resultData.originalUrl,
            options: {
              debug: false
            }
          }
        })

        if (result.result && result.result.title) {
          // 统一解析器直接返回标准化结果
          this.resultData = result.result
          console.log('重新解析成功，已更新结果数据')
          return this.resultData
        } else {
          throw new Error(result.result?.content || result.result?.message || '重新解析失败')
        }
      } catch (error) {
        console.error(`重新解析失败 (第${retryCount + 1}次):`, error)

        // 如果还有重试次数，等待一下再重试
        if (retryCount < maxRetries) {
          // 随机延迟1-3秒，避免被识别为机器人
          const delay = 1000 + Math.random() * 2000
          console.log(`等待${Math.round(delay/1000)}秒后进行第${retryCount + 2}次重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
          return await this.reParseVideo(retryCount + 1)
        } else {
          throw error
        }
      }
    },

    // 图片预览相关方法
    // 获取当前显示的图片URL
    getCurrentImageUrl() {
      if (this.resultData.processedData?.imageUrls && this.resultData.processedData.imageUrls.length > 0) {
        const url = this.resultData.processedData.imageUrls[this.currentImageIndex] || this.resultData.processedData.imageUrls[0];
        
        // 调试：打印URL信息
        console.log('🔍 前端getCurrentImageUrl调试:', {
          当前索引: this.currentImageIndex,
          URL类型: typeof url,
          URL内容: url,
          前3个URLs: this.resultData.processedData.imageUrls.slice(0, 3).map(u => ({ 类型: typeof u, 内容: u }))
        });
        
        return url;
      }
      return this.resultData.processedData?.data || ''
    },

    // 选择图片
    selectImage(index) {
      

      // 如果选择的是当前图片，不需要切换
      if (this.currentImageIndex === index) {
        return
      }

      const imageUrl = this.resultData.processedData.imageUrls[index]

      // 如果图片已经预加载，直接切换
      if (this.preloadedImages.has(imageUrl)) {
        this.currentImageIndex = index
      } else {
        // 显示加载状态
        this.imageLoading = true
        this.currentImageIndex = index
      }

      // 可以添加一些反馈效果
      uni.vibrateShort({
        type: 'light'
      })

      // 预加载相邻的图片
      this.preloadAdjacentImages(index)
    },

    // 预加载相邻图片
    preloadAdjacentImages(currentIndex) {
      const imageUrls = this.resultData.processedData?.imageUrls || []
      const preloadIndexes = []

      // 预加载前后各2张图片
      for (let i = -2; i <= 2; i++) {
        const index = currentIndex + i
        if (index >= 0 && index < imageUrls.length && index !== currentIndex) {
          preloadIndexes.push(index)
        }
      }

      preloadIndexes.forEach(index => {
        const imageUrl = imageUrls[index]
        if (!this.preloadedImages.has(imageUrl)) {
          this.preloadImage(imageUrl)
        }
      })
    },

    // 预加载单张图片（小程序环境下使用uni.getImageInfo）
    preloadImage(imageUrl) {
      uni.getImageInfo({
        src: imageUrl,
        success: () => {
          this.preloadedImages.add(imageUrl)
        },
        fail: () => {
          // 静默处理预加载失败，避免控制台噪音
        }
      })
    },

    // Live Photo 相关方法
    // 检查指定图片是否有对应的Live Photo
    hasLivePhotoForImage(imageIndex) {
      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      const videoUrls = this.resultData.processedData?.videoUrls;
      
      // 调试：打印Live Photo数据
      if (imageIndex === 0) {
        console.log('🔍 前端Live Photo数据调试:', {
          livePhotoVideos长度: livePhotoVideos ? livePhotoVideos.length : 'N/A',
          前3个livePhotoVideos: livePhotoVideos ? livePhotoVideos.slice(0, 3).map(v => ({ 类型: typeof v, 内容: v })) : 'N/A',
          videoUrls长度: videoUrls ? videoUrls.length : 'N/A',
          前3个videoUrls: videoUrls ? videoUrls.slice(0, 3).map(v => ({ 类型: typeof v, 内容: v })) : 'N/A'
        });
      }
      
      // 如果有livePhotoVideos数据且不全为null，使用新逻辑
      const hasValidLivePhotoData = livePhotoVideos && 
                                   livePhotoVideos.length > 0 && 
                                   livePhotoVideos.some(video => video !== null);
      
      if (hasValidLivePhotoData) {
        return livePhotoVideos.length > imageIndex &&
               livePhotoVideos[imageIndex] &&
               livePhotoVideos[imageIndex] !== null;
      }
      
      // 备用逻辑：使用原来的videoUrls逻辑
      return videoUrls &&
             videoUrls.length > imageIndex &&
             videoUrls[imageIndex];
    },

    // 播放指定图片的Live Photo
    playLivePhotoForImage(imageIndex) {

      if (!this.hasLivePhotoForImage(imageIndex)) {
        uni.showToast({
          title: '该图片没有Live Photo',
          icon: 'none'
        })
        return
      }

      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      const videoUrls = this.resultData.processedData?.videoUrls;
      
      let videoUrl;
      if (livePhotoVideos && livePhotoVideos[imageIndex]) {
        videoUrl = livePhotoVideos[imageIndex];
      } else if (videoUrls && videoUrls[imageIndex]) {
        videoUrl = videoUrls[imageIndex];
      }
      
      this.currentLivePhotoIndex = imageIndex
      this.currentLivePhotoUrl = videoUrl || ''
      this.showLivePhotoPlayer = true

      console.log('打开Live Photo播放器:', videoUrl)
    },

    // 关闭Live Photo播放器
    closeLivePhotoPlayer() {
      this.showLivePhotoPlayer = false
      this.currentLivePhotoUrl = ''
      this.currentLivePhotoIndex = 0

      // 停止视频播放
      try {
        const videoContext = uni.createVideoContext('livePhotoPlayerVideo', this)
        if (videoContext) {
          videoContext.pause()
        }
      } catch (error) {
        console.error('停止视频播放失败:', error)
      }
    },

    // Live Photo播放器视频结束
    onLivePhotoPlayerEnded() {
      console.log('Live Photo播放结束')
      // 可以选择自动关闭播放器或者循环播放
      // this.closeLivePhotoPlayer()
    },

    // 下载当前Live Photo
    async downloadCurrentLivePhoto() {
      if (!this.currentLivePhotoUrl) {
        uni.showToast({
          title: '没有可下载的视频',
          icon: 'none'
        })
        return
      }

      await this.downloadLivePhoto(this.currentLivePhotoUrl, this.currentLivePhotoIndex)
    },

    // 静默下载 Live Photo（用于批量下载，不显示单独的加载提示）
    async downloadLivePhotoSilent(videoUrl, index) {
      console.log('静默下载 Live Photo:', videoUrl, 'index:', index)

      return new Promise(async (resolve, reject) => {
        try {
          // 参数类型检查
          if (!videoUrl || typeof videoUrl !== 'string') {
            reject(new Error('无效的视频URL参数'))
            return
          }

          // 检查相册权限
          await this.checkAlbumPermission()

          // 🔧 为微博Live Photo添加正确的请求头
          const headers = {}
          if (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn') || 
              (videoUrl.includes('video.weibo.com') && videoUrl.includes('livephoto='))) {
            headers['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            headers['Referer'] = 'https://weibo.com/'
            headers['Accept'] = '*/*'
            headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
            headers['Cache-Control'] = 'no-cache'
            headers['Pragma'] = 'no-cache'
          }

          const downloadTask = uni.downloadFile({
            url: videoUrl,
            filePath: `${wx.env.USER_DATA_PATH}/livephoto_${Date.now()}.mp4`,
            header: headers,
            timeout: 30000,
            success: (res) => {
              console.log('Live Photo 下载完成，状态码:', res.statusCode)
              if (res.statusCode === 200) {
                console.log('Live Photo 下载成功，开始保存到相册:', res.filePath || res.tempFilePath)

                // 保存到相册
                uni.saveVideoToPhotosAlbum({
                  filePath: res.filePath || res.tempFilePath,
                  success: () => {
                    console.log(`Live Photo ${index + 1} 保存到相册成功`)
                    resolve()
                  },
                  fail: (error) => {
                    console.error('保存 Live Photo 到相册失败:', error)
                    reject(error)
                  }
                })
              } else {
                const error = new Error(`下载失败，状态码: ${res.statusCode}`)
                console.error('Live Photo 下载失败:', error)
                reject(error)
              }
            },
            fail: (error) => {
              console.error('Live Photo 下载请求失败:', error)
              reject(error)
            }
          })

          // 保存下载任务引用，以便取消
          this.currentDownloadTask = downloadTask

        } catch (error) {
          console.error('下载 Live Photo 过程中出错:', error)
          reject(error)
        }
      })
    },

    // 下载 Live Photo
    async downloadLivePhoto(videoUrl, index) {
      console.log('开始下载 Live Photo:', videoUrl, 'index:', index)

      return new Promise(async (resolve, reject) => {
        try {
          // 参数类型检查
          if (!videoUrl || typeof videoUrl !== 'string') {
            reject(new Error('无效的视频URL参数'))
            return
          }

          // 检查相册权限
          await this.checkAlbumPermission()

          this.showCustomLoading(`正在下载 Live Photo ${index + 1}...`)

          // 🔧 为微博Live Photo添加正确的请求头
          const headers = {}
          if (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn') || 
              (videoUrl.includes('video.weibo.com') && videoUrl.includes('livephoto='))) {
            headers['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            headers['Referer'] = 'https://weibo.com/'
            headers['Accept'] = '*/*'
            headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
            headers['Cache-Control'] = 'no-cache'
            headers['Pragma'] = 'no-cache'
          }

          const downloadTask = uni.downloadFile({
            url: videoUrl,
            filePath: `${wx.env.USER_DATA_PATH}/livephoto_${Date.now()}.mp4`,
            header: headers,
            timeout: 30000,
            success: (res) => {
              console.log('Live Photo 下载完成，状态码:', res.statusCode)
              if (res.statusCode === 200) {
                console.log('Live Photo 下载成功，开始保存到相册:', res.filePath || res.tempFilePath)

                // 保存到相册
                uni.saveVideoToPhotosAlbum({
                  filePath: res.filePath || res.tempFilePath,
                  success: () => {
                    console.log(`Live Photo ${index + 1} 保存到相册成功`)
                    this.hideCustomLoading()
                    if (this.isPageActive) {
                      uni.showToast({
                        title: `Live Photo ${index + 1} 已保存`,
                        icon: 'success'
                      })
                    }
                    resolve()
                  },
                  fail: (error) => {
                    console.error('保存 Live Photo 到相册失败:', error)
                    this.hideCustomLoading()
                    if (this.isPageActive) {
                      uni.showToast({
                        title: '保存失败',
                        icon: 'none'
                      })
                    }
                    reject(error)
                  }
                })
              } else {
                const error = new Error(`下载失败，状态码: ${res.statusCode}`)
                console.error('Live Photo 下载失败:', error)
                this.hideCustomLoading()
                if (this.isPageActive) {
                  uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                  })
                }
                reject(error)
              }
            },
            fail: (error) => {
              console.error('Live Photo 下载请求失败:', error)
              this.hideCustomLoading()
              if (this.isPageActive) {
                uni.showToast({
                  title: '下载失败',
                  icon: 'none'
                })
              }
              reject(error)
            }
          })

          // 保存下载任务引用，以便取消
          this.currentDownloadTask = downloadTask

        } catch (error) {
          console.error('下载 Live Photo 过程中出错:', error)
          this.hideCustomLoading()
          if (this.isPageActive) {
            uni.showToast({
              title: error.message || '下载失败',
              icon: 'none'
            })
          }
          reject(error)
        }
      })
    },

    // 获取文案
    getCopyText() {
      let shareText = ''
      
      // 通用逻辑：有文案就组合标题和文案，没有文案就只复制标题
      if (this.resultData.content && this.resultData.content.trim()) {
        // 有文案内容时，组合标题和文案
        shareText = `${this.resultData.title}\n\n${this.resultData.content}`
      } else {
        // 没有文案时，只使用标题
        shareText = this.resultData.title
      }

      uni.setClipboardData({
        data: shareText,
        success: () => {
          if (this.isPageActive) {
            const toastTitle = '已复制文案'
            
            uni.showToast({
              title: toastTitle,
              icon: 'success'
            })
          }
        }
      })
    },

    // 获取封面
    async getCover() {
      if (!this.resultData) {
        uni.showToast({
          title: '没有可获取的封面',
          icon: 'none'
        })
        return
      }

      try {
        // 检查相册权限
        await this.checkAlbumPermission()

        this.showCustomLoading('正在获取封面...')

        let coverUrl = null

        // 根据内容类型获取封面
        if (this.resultData.type === 'image') {
          // 图文内容，优先使用已有的封面URL
          if (this.resultData.coverUrl) {
            coverUrl = this.resultData.coverUrl
          } else {
            // 如果没有封面URL，使用备用方案
            if (this.resultData.processedData.imageUrls && this.resultData.processedData.imageUrls.length > 0) {
              coverUrl = this.resultData.processedData.imageUrls[0]
            } else if (this.resultData.processedData.data) {
              coverUrl = this.resultData.processedData.data
            }
          }
        } else if (this.resultData.type === 'video') {
          // 视频内容，优先使用已有的封面URL
          if (this.resultData.coverUrl) {
            coverUrl = this.resultData.coverUrl
          } else {
            
            if (!this.resultData.originalUrl) {
              throw new Error('缺少原始链接，无法获取封面')
            }

            try {
              // 使用统一解析器获取封面
              const result = await uniCloud.callFunction({
                name: 'unified-parser',
                data: {
                  link: this.resultData.originalUrl,
                  options: {
                    debug: false
                  }
                }
              })



              if (result.result && result.result.coverUrl) {
                coverUrl = result.result.coverUrl
              } else {
                throw new Error('云函数未返回有效的封面URL')
              }
            } catch (cloudError) {
              console.error('云函数获取封面失败:', cloudError)
              throw new Error('无法获取视频封面，请重试')
            }
          }
        }

        if (!coverUrl) {
          this.hideCustomLoading()
          uni.showToast({
            title: '无法获取封面',
            icon: 'none'
          })
          return
        }


        
        // 修复B站封面URL协议问题
        if (coverUrl) {
          const originalUrl = coverUrl
          
          // 处理相对协议URL（//domain.com）
          if (coverUrl.startsWith('//')) {
            coverUrl = 'https:' + coverUrl
          }
          // 处理HTTP协议（转换为HTTPS）
          else if (coverUrl.startsWith('http://')) {
            coverUrl = coverUrl.replace('http://', 'https://')
          }
          
          // 移除B站图片处理参数（@100w_100h_1c.png等）
          if (coverUrl.includes('@') && coverUrl.includes('hdslb.com')) {
            coverUrl = coverUrl.split('@')[0]
          }
        }

        // 下载并保存封面
        await this.saveCoverToAlbum(coverUrl)

        this.hideCustomLoading()
        uni.showModal({
          title: '保存成功',
          content: '视频封面已成功保存到您的相册中',
          showCancel: false,
          confirmText: '知道了'
        })

      } catch (error) {
        console.error('获取封面失败:', error)
        this.hideCustomLoading()
        uni.showModal({
          title: '获取封面失败',
          content: error.message || '获取封面时出现错误，请重试',
          showCancel: false
        })
      }
    },

    // 保存封面到相册
    async saveCoverToAlbum(coverUrl) {
      if (!coverUrl || typeof coverUrl !== 'string') {
        console.error('❌ 无效的封面URL:', coverUrl)
        throw new Error('封面URL无效')
      }
      
      return new Promise((resolve, reject) => {
        
        uni.downloadFile({
          url: coverUrl,
          timeout: 30000,
          success: (res) => {
            if (res.statusCode === 200 && res.tempFilePath) {
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  resolve()
                },
                fail: (error) => {
                  console.error('封面保存到相册失败:', error)
                  reject(new Error('封面保存失败，请检查相册权限'))
                }
              })
            } else {
              reject(new Error('封面下载失败'))
            }
          },
          fail: (error) => {
            console.error('❌ downloadFile失败:', error)
            reject(new Error(`封面下载失败: ${error.errMsg || '未知错误'}`))
          }
        })
      })
    },
    
    // 复制当前显示图片的链接
    copyCurrentImageUrl() {
      this.copyImageUrl(this.currentImageIndex)
    },

    // 复制指定图片链接
    copyImageUrl(imageIndex) {
      // 检查是否有可复制的链接（URL模式或图片URLs）
      const hasUrl = this.resultData.processedData && this.resultData.processedData.isUrl;
      const hasImageUrls = this.resultData.processedData && this.resultData.processedData.imageUrls && this.resultData.processedData.imageUrls.length > 0;
      
      if (!hasUrl && !hasImageUrls) {
        if (this.isPageActive) {
          uni.showToast({
            title: '当前为本地数据，无法复制链接',
            icon: 'none'
          })
        }
        return
      }

      const imageUrls = this.resultData.processedData?.imageUrls || []
      if (imageUrls.length === 0 || !imageUrls[imageIndex]) {
        if (this.isPageActive) {
          uni.showToast({
            title: '无法获取图片链接',
            icon: 'none'
          })
        }
        return
      }

      uni.setClipboardData({
        data: imageUrls[imageIndex],
        success: () => {
          if (this.isPageActive) {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          }
        }
      })
    },

    // 复制视频链接
    copyVideoUrl() {
      
      if (this.resultData.processedData && this.resultData.processedData.isUrl) {
        let urlToCopy = this.resultData.processedData.data
        let toastTitle = '链接已复制'
        
        // 如果是图文内容，复制当前显示的图片链接
        if (this.resultData.type === 'image') {
          const imageUrls = this.resultData.processedData?.imageUrls || []
          if (imageUrls.length > 0) {
            urlToCopy = imageUrls[this.currentImageIndex] || imageUrls[0]
          }
        }
        
        uni.setClipboardData({
          data: urlToCopy,
          success: () => {
            if (this.isPageActive) {
              uni.showToast({
                title: toastTitle,
                icon: 'success'
              })
            }
          }
        })
      } else {
        if (this.isPageActive) {
          uni.showToast({
            title: '当前为本地数据，无法复制链接',
            icon: 'none'
          })
        }
      }
    },

    // 检查相册权限
    async checkAlbumPermission() {
      return new Promise((resolve, reject) => {
        uni.getSetting({
          success: (res) => {
            if (res.authSetting['scope.writePhotosAlbum'] === false) {
              // 用户拒绝过权限，需要引导到设置页面
              uni.showModal({
                title: '需要相册权限',
                content: '保存视频需要访问您的相册权限，请在设置中开启',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          resolve()
                        } else {
                          reject(new Error('用户未开启相册权限'))
                        }
                      },
                      fail: () => reject(new Error('打开设置失败'))
                    })
                  } else {
                    reject(new Error('用户取消授权'))
                  }
                }
              })
            } else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
              // 未授权过，直接请求授权
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => resolve(),
                fail: () => reject(new Error('用户拒绝授权'))
              })
            } else {
              // 已授权
              resolve()
            }
          },
          fail: () => reject(new Error('获取权限状态失败'))
        })
      })
    },

    // 简单直接的视频下载（带重试机制）
    async downloadVideo(videoUrl, retryCount = 0, skipReparse = false) {
      const maxRetries = 2 // 最多重试2次

      // 参数类型检查
      if (!videoUrl || typeof videoUrl !== 'string') {
        throw new Error('无效的视频URL参数')
      }

      console.log('开始下载视频:', videoUrl)
      console.log('当前重试次数:', retryCount)
      console.log('视频URL类型:', typeof videoUrl)
      console.log('视频URL长度:', videoUrl.length)

      if (!videoUrl.startsWith('http')) {
        throw new Error('视频URL格式不正确')
      }

      // 第一次尝试时测试URL可访问性，重试时跳过以节省时间
      if (retryCount === 0) {
        try {
          await this.testVideoUrl(videoUrl)
        } catch (error) {
          console.error('视频URL测试失败:', error)
          throw new Error('视频链接无法访问，可能已失效，请重新解析')
        }
      }

      const isLongVideo = this.resultData.processedData?.isLongVideo || false
      // 根据网络环境调整超时时间
      const systemInfo = uni.getSystemInfoSync()
      const isDevTool = systemInfo.platform === 'devtools'

      // 开发工具环境使用更长的超时时间，特别是热点网络
      let timeout
      if (isDevTool) {
        timeout = isLongVideo ? 900000 : 600000 // 开发工具：长视频15分钟，短视频10分钟
      } else {
        timeout = isLongVideo ? 600000 : 300000 // 真机：长视频10分钟，短视频5分钟
      }

      console.log('设置超时时间:', timeout / 1000, '秒')

      // 检查页面是否还活跃
      if (!this.isPageActive) {
        console.log('页面已离开，停止下载')
        throw new Error('页面已离开，下载已取消')
      }

      return new Promise((resolve, reject) => {
        // 构建请求头，移除可能被拒绝的User-Agent
        const headers = {}

        // 根据URL来源设置合适的Referer
        if (videoUrl.includes('douyin') || videoUrl.includes('aweme')) {
          headers['Referer'] = 'https://www.douyin.com/'
        } else if (videoUrl.includes('xiaohongshu')) {
          headers['Referer'] = 'https://www.xiaohongshu.com/'
        } else if (videoUrl.includes('bilibili') || videoUrl.includes('bilivideo')) {
          // B站视频需要特殊的请求头
          headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          headers['Referer'] = 'https://www.bilibili.com/'
          headers['Origin'] = 'https://www.bilibili.com'
        } else if (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn') || 
                   (videoUrl.includes('video.weibo.com') && videoUrl.includes('livephoto='))) {
          // 🔧 微博视频需要特殊的请求头模拟curl -O行为
          headers['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          headers['Referer'] = 'https://weibo.com/'
          headers['Accept'] = '*/*'
          headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
          headers['Cache-Control'] = 'no-cache'
          headers['Pragma'] = 'no-cache'
        }

        const downloadTask = uni.downloadFile({
          url: videoUrl,
          timeout: timeout,
          header: headers,

          success: async (res) => {
            console.log('下载结果:', res)
            if (res.statusCode === 200 && res.tempFilePath) {
              console.log('下载成功，文件路径:', res.tempFilePath)

              // 检查响应头中的Content-Type
              if (res.header && res.header['content-type']) {
                const contentType = res.header['content-type'].toLowerCase()
                console.log('响应Content-Type:', contentType)

                // 如果返回的是JSON或HTML，说明不是视频文件
                if (contentType.includes('application/json') || contentType.includes('text/html')) {
                  console.error('服务器返回的不是视频文件，Content-Type:', contentType)
                  if (skipReparse) {
                    reject(new Error('服务器返回的不是视频文件，链接可能已失效'))
                  } else {
                    reject(new Error('NEED_REPARSE:服务器返回的不是视频文件，链接可能已失效'))
                  }
                  return
                }
              }

              // 验证下载的文件是否有效
              try {
                await this.validateDownloadedFile(res.tempFilePath)
                resolve(res.tempFilePath)
              } catch (error) {
                console.error('下载文件验证失败:', error)
                // 直接传递错误，validateDownloadedFile已经添加了NEED_REPARSE前缀
                reject(error)
              }
            } else {
              console.log('下载失败，状态码:', res.statusCode)
              let errorMsg = `下载失败，状态码: ${res.statusCode}`
              if (res.statusCode === 403) {
                if (videoUrl.includes('bilibili') || videoUrl.includes('bilivideo')) {
                  errorMsg = 'B站视频访问被拒绝，建议复制链接到浏览器下载'
                } else {
                  errorMsg = '视频链接访问被拒绝，可能已失效，请重新解析'
                }
              } else if (res.statusCode === 404) {
                errorMsg = '视频文件不存在，链接可能已失效'
              } else if (res.statusCode >= 500) {
                errorMsg = '服务器错误，请稍后重试'
              }
              reject(new Error(errorMsg))
            }
          },
          fail: async (error) => {
            console.error('下载失败:', error)

            // 检查是否可以重试
            const canRetry = retryCount < maxRetries && (
              (error.errMsg && error.errMsg.includes('timeout')) ||
              (error.errMsg && error.errMsg.includes('network')) ||
              (error.errMsg && error.errMsg.includes('fail'))
            )

            if (canRetry) {

              // 等待一段时间后重试
              const retryDelay = (retryCount + 1) * 2000 // 2秒、4秒、6秒

              setTimeout(async () => {
                try {
                  const retryResult = await this.downloadVideo(videoUrl, retryCount + 1)
                  resolve(retryResult)
                } catch (retryError) {
                  reject(retryError)
                }
              }, retryDelay)

              return // 不执行下面的reject
            }

            // 不能重试或重试次数已用完，返回错误
            let errorMsg = '下载失败'
            if (error.errMsg && error.errMsg.includes('ENOENT')) {
              errorMsg = '文件保存失败，这可能是微信开发工具的问题。真机测试通常正常，或者可以复制链接到浏览器下载'
            } else if (error.errMsg && error.errMsg.includes('timeout')) {
              errorMsg = `下载超时（已重试${retryCount}次），请检查网络连接。建议在WiFi环境下重试`
            } else if (error.errMsg && (error.errMsg.includes('fail') || error.errMsg.includes('network'))) {
              errorMsg = `网络连接失败（已重试${retryCount}次），请检查网络连接后重试`
            } else if (error.errMsg && error.errMsg.includes('abort')) {
              errorMsg = '下载被中断'
            } else {
              errorMsg = `下载失败（已重试${retryCount}次），请检查网络连接或稍后重试`
            }
            reject(new Error(errorMsg))
          }
        })

            // 存储下载任务引用
            this.currentDownloadTask = downloadTask

        // 监听下载进度
        // 添加节流控制，避免频繁更新UI和打印日志
        let lastProgressUpdate = 0
        let lastProgress = -1
        let lastDownloadedMB = -1
        let downloadCompleted = false
        
        downloadTask.onProgressUpdate((res) => {
          // 检查页面是否还活跃
          if (!this.isPageActive) {
            return
          }
          
          // 如果下载已完成，不再处理进度更新
          if (downloadCompleted) {
            return
          }

          const progress = Math.round(res.progress)
          const downloadedMB = Math.round(res.totalBytesWritten / 1024 / 1024 * 10) / 10
          const totalMB = Math.round(res.totalBytesExpectedToWrite / 1024 / 1024 * 10) / 10
          
          // 节流控制：只在进度变化或下载量变化超过0.5MB时更新
          const now = Date.now()
          const progressChanged = progress !== lastProgress
          const downloadedChanged = Math.abs(downloadedMB - lastDownloadedMB) >= 0.5
          const timeElapsed = now - lastProgressUpdate > 500 // 至少间隔500ms
          
          if (progressChanged || downloadedChanged || timeElapsed || progress >= 100) {
            // 更新上次记录的值
            lastProgress = progress
            lastDownloadedMB = downloadedMB
            lastProgressUpdate = now

            // 显示详细的进度信息
            if (progress > 0 && progress < 100 && res.totalBytesWritten > 0) {
              let title = `下载中 ${progress}%`
              if (totalMB > 0) {
                title += ` (${downloadedMB}/${totalMB}MB)`
              }
              this.showCustomLoading(title)
            } else if (progress >= 100) {
              this.showCustomLoading('下载完成，正在保存到相册...')
              // 标记下载完成，避免后续的进度更新
              downloadCompleted = true
            } else if (res.totalBytesWritten > 0) {
              this.showCustomLoading(`下载中... (${downloadedMB}MB)`)
            }
          }
        })
      })
    },

    // 测试视频URL是否可访问
    async testVideoUrl(videoUrl) {
      return new Promise((resolve, reject) => {
        // 检查参数是否有效
        if (!videoUrl || typeof videoUrl !== 'string') {
          console.log('无效的URL参数，跳过可访问性测试')
          resolve()
          return
        }

        // 检查是否为小红书页面链接，如果是则直接通过测试
        if (videoUrl.includes('xiaohongshu.com') && !videoUrl.includes('.mp4') && !videoUrl.includes('.m3u8')) {
          console.log('小红书页面链接，跳过可访问性测试')
          resolve()
          return
        }

        // 检查是否为抖音API地址，如果是则直接通过测试
        if (videoUrl.includes('aweme.snssdk.com') || videoUrl.includes('aweme-')) {
          console.log('抖音API地址，跳过可访问性测试')
          resolve()
          return
        }

        // 🔧 检查是否为微博URL，如果是则直接通过测试（微博CDN拒绝HEAD请求但允许GET请求）
        if (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn') || 
            (videoUrl.includes('video.weibo.com') && videoUrl.includes('livephoto='))) {
          console.log('微博URL，跳过HEAD请求检查（微博CDN拒绝HEAD但允许GET）')
          resolve()
          return
        }

        // 根据环境调整测试超时时间
        const systemInfo = uni.getSystemInfoSync()
        const isDevTool = systemInfo.platform === 'devtools'
        const testTimeout = isDevTool ? 30000 : 15000 // 开发工具30秒，真机15秒

        console.log('测试URL可访问性，超时时间:', testTimeout / 1000, '秒')

        // 构建请求头，移除可能被拒绝的User-Agent
        const headers = {}

        // 根据URL来源设置合适的Referer
        if (videoUrl.includes('douyin') || videoUrl.includes('aweme')) {
          headers['Referer'] = 'https://www.douyin.com/'
        } else if (videoUrl.includes('xiaohongshu')) {
          headers['Referer'] = 'https://www.xiaohongshu.com/'
        } else if (videoUrl.includes('bilibili') || videoUrl.includes('bilivideo')) {
          // B站视频需要特殊的请求头
          headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          headers['Referer'] = 'https://www.bilibili.com/'
          headers['Origin'] = 'https://www.bilibili.com'
        }

        uni.request({
          url: videoUrl,
          method: 'HEAD',
          timeout: testTimeout,
          header: headers,
          success: (res) => {
            console.log('URL测试结果，状态码:', res.statusCode)
            if (res.statusCode === 200) {
              resolve()
            } else {
              reject(new Error(`URL测试失败，状态码: ${res.statusCode}`))
            }
          },
          fail: (error) => {
            console.log('URL测试失败:', error)
            reject(error)
          }
        })
      })
    },

    // 验证下载的文件是否有效
    async validateDownloadedFile(filePath) {
      return new Promise((resolve, reject) => {
        uni.getFileInfo({
          filePath: filePath,
          success: (res) => {
            console.log('文件信息:', res)

            // 检查文件大小
            if (res.size <= 0) {
              reject(new Error('NEED_REPARSE:下载的文件为空'))
              return
            }

            // 检查文件扩展名，如果是.json说明下载的不是视频文件
            if (filePath.toLowerCase().endsWith('.json')) {
              console.error('下载的文件是JSON格式，不是视频文件')
              reject(new Error('NEED_REPARSE:服务器返回的不是视频文件，可能是错误信息或链接已失效'))
              return
            }

            // 检查文件大小是否过小（可能是错误页面）
            if (res.size < 1024) { // 小于1KB的文件很可能不是正常的视频
              console.error('下载的文件过小:', res.size, 'bytes')
              reject(new Error('NEED_REPARSE:下载的文件过小，可能不是有效的视频文件'))
              return
            }

            resolve()
          },
          fail: (error) => {
            console.error('获取文件信息失败:', error)
            reject(new Error('无法验证下载的文件'))
          }
        })
      })
    },

    // 加载文件大小（仅用于纯视频内容）
    async loadFileSize() {
      if (!this.resultData.processedData || !this.resultData.processedData.isUrl) {
        return
      }

      try {
        const size = await this.checkVideoSize(this.resultData.processedData.data)
        if (size > 0) {
          this.cachedFileSize = size
          console.log('视频文件大小已缓存:', this.formatFileSize(size))
        }
      } catch (error) {
        console.log('加载视频文件大小失败:', error)
      }
    },

    // 检查视频文件大小
    async checkVideoSize(videoUrl) {
      try {
        // 检查参数是否有效
        if (!videoUrl || typeof videoUrl !== 'string') {
          console.log('无效的URL参数，跳过文件大小检查')
          return 0
        }

        // 检查是否为小红书页面链接，如果是则跳过大小检查
        if (videoUrl.includes('xiaohongshu.com') && !videoUrl.includes('.mp4') && !videoUrl.includes('.m3u8')) {
          console.log('小红书页面链接，跳过文件大小检查')
          return 0
        }

        // 🔧 检查是否为微博URL，如果是则跳过大小检查（微博CDN拒绝HEAD请求但允许GET请求）
        if (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn') || 
            (videoUrl.includes('video.weibo.com') && videoUrl.includes('livephoto='))) {
          console.log('微博URL，跳过HEAD请求检查（微博CDN拒绝HEAD但允许GET）')
          return 0
        }

        // 检查是否为直接的视频文件URL
        const isDirectVideoUrl = videoUrl.includes('.mp4') || videoUrl.includes('.m3u8') ||
                                 videoUrl.includes('aweme.snssdk.com') ||
                                 videoUrl.includes('douyinvod.com')

        if (!isDirectVideoUrl) {
          console.log('非直接视频URL，跳过文件大小检查')
          return 0
        }

        const response = await new Promise((resolve, reject) => {
          // 构建请求头，移除可能被拒绝的User-Agent
          const headers = {}

          // 根据URL来源设置合适的Referer
          if (videoUrl.includes('douyin') || videoUrl.includes('aweme')) {
            headers['Referer'] = 'https://www.douyin.com/'
          } else if (videoUrl.includes('xiaohongshu')) {
            headers['Referer'] = 'https://www.xiaohongshu.com/'
          }

          uni.request({
            url: videoUrl,
            method: 'HEAD',
            timeout: 10000,
            header: headers,
            success: resolve,
            fail: reject
          })
        })

        const contentLength = response.header['Content-Length'] || response.header['content-length']
        const size = parseInt(contentLength) || 0

        // 更新缓存
        if (size > 0) {
          this.cachedFileSize = size
        }

        return size

      } catch (error) {
        console.log('获取文件大小失败:', error)
        return 0 // 如果获取失败，返回0，不阻止下载
      }
    },

    // 处理可能需要代理的URL（目前直接返回原始URL）
    maybeProxyUrl(originalUrl) {
      // 由于已禁用代理服务，直接返回原始URL
      // 微博的视频和Live Photo在真机环境可以直接访问
      return originalUrl;
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #F5F5F5;
}

/* 自定义Loading样式 */
.custom-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.custom-loading-content {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  padding: 40rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 400rpx;
  max-width: 600rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #ffffff;
  font-size: 32rpx;
  text-align: center;
  line-height: 1.4;
  word-break: break-all;
  white-space: normal;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #ffffff;
  border-bottom: 1rpx solid #E5E5E5;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(0, 122, 255, 0.1);
  transition: all 0.3s ease;
  min-width: 120rpx;
  justify-content: center;
}

.back-btn:active {
  background: rgba(0, 122, 255, 0.2);
  transform: scale(0.95);
}

.back-btn-inner {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #007AFF;
  margin-right: 8rpx;
  font-weight: 600;
  line-height: 1;
}

.back-text {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  flex: 1;
}

.placeholder {
  width: 120rpx;
  display: flex;
  justify-content: flex-end;
}

.video-info {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.video-content {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
  white-space: pre-wrap; /* 保持换行格式 */
  word-break: break-all;
}

.video-author,
.video-source {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.video-section {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.video-container {
  position: relative;
}

.bilibili-warning {
  background: #E3F2FD;
  border: 1rpx solid #BBDEFB;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.long-video-warning {
  background: #FFF3CD;
  border: 1rpx solid #FFEAA7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.warning-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
}

.warning-text {
  font-size: 24rpx;
  color: #856404;
  flex: 1;
  line-height: 1.4;
}

.bilibili-warning .warning-text {
  color: #1976D2;
}

.main-video {
  width: 100%;
  height: 500rpx;
  border-radius: 12rpx;
  background: #000;
}

.image-content {
  width: 100%;
}

.main-image-container {
  position: relative;
  width: 100%;
  height: 500rpx; /* 固定高度，防止布局抖动 */
  border-radius: 12rpx;
  overflow: hidden;
  background: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background: #F5F5F5;
  transition: opacity 0.3s ease;
}

.main-image.image-hidden {
  opacity: 0;
}

.image-loading-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;
  z-index: 1;
}

.loading-spinner-small {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5E5;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text-small {
  font-size: 24rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-live-photo-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10rpx);
}

.main-copy-link-btn {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10rpx);
  z-index: 2;
}

.main-copy-icon {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
}

.main-copy-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.main-play-icon {
  width: 24rpx;
  height: 24rpx;
  color: #ffffff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-live-text {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.main-image-info {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-counter {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.live-indicator {
  background: rgba(255, 107, 129, 0.9);
  color: #ffffff;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  backdrop-filter: blur(10rpx);
}

.image-gallery {
  margin-top: 30rpx;
}

.gallery-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.gallery-scroll {
  white-space: nowrap;
  height: 200rpx;
}

.gallery-item {
  display: inline-block;
  margin-right: 20rpx;
  width: 160rpx;
  height: 160rpx;
}

.live-photo-count-inline {
  color: #007AFF;
  font-size: 26rpx;
  margin-left: 10rpx;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.gallery-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background: #F5F5F5;
}

.live-photo-play-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  backdrop-filter: blur(10rpx);
}


.play-icon-small {
  width: 20rpx;
  height: 20rpx;
  color: #ffffff;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.live-text {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.image-index {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 30rpx;
  text-align: center;
}

.selected-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: #007AFF;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.image-container.active {
  transform: scale(0.95);
  opacity: 0.8;
}

.image-container.active .gallery-image {
  border: 4rpx solid #007AFF;
}

/* Live Photo 播放器样式 */
.live-photo-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.live-photo-player-container {
  width: 100%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  max-height: 80vh;
}

.live-photo-player-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #E5E5E5;
}

.live-photo-player-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.live-photo-player-close {
  width: 60rpx;
  height: 60rpx;
  background: #E5E5E5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  cursor: pointer;
}

.live-photo-player-video {
  width: 100%;
  height: 400rpx;
  background: #000000;
}

.live-photo-player-actions {
  padding: 30rpx;
  display: flex;
  justify-content: center;
}

.live-photo-player-btn {
  background: linear-gradient(45deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.video-status {
  text-align: center;
  margin-top: 20rpx;
}

.status-text {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
  background: #F0F0F0;
  border-radius: 20rpx;
}

.action-section {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.action-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-row:last-child {
  margin-bottom: 0;
}

.action-btn {
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
}

.primary-btn {
  background: linear-gradient(45deg, #007AFF, #5AC8FA);
  color: #ffffff;
}

.secondary-btn {
  background: #F8F9FA;
  color: #333;
  border: 1rpx solid #E5E5E5;
}

.btn-icon {
  margin-right: 15rpx;
  font-size: 32rpx;
}

.process-info {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.info-value.success {
  color: #34C759;
}

.info-value.error {
  color: #FF3B30;
}

.disclaimer {
  background: #FFF2F2;
  border: 1rpx solid #FFB3B3;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.disclaimer-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #D32F2F;
  display: block;
  margin-bottom: 15rpx;
}

.disclaimer-text {
  font-size: 24rpx;
  color: #D32F2F;
  line-height: 1.8;
}
</style>