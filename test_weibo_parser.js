// 测试微博解析器的ID提取功能

// 模拟DEBUG变量
const DEBUG = true;

// 复制相关函数进行测试
function extractWeiboIdFromUrl(url) {
  if (!url) return '';
  
  if (DEBUG) console.log('提取微博ID，原始URL:', url);
  
  // 首先尝试从URL参数中解码原始URL（处理重定向情况）
  if (url.includes('passport.weibo.com/visitor/visitor') && url.includes('url=')) {
    try {
      const urlParam = url.match(/[?&]url=([^&]+)/);
      if (urlParam) {
        const decodedUrl = decodeURIComponent(urlParam[1]);
        if (DEBUG) console.log('🔍 从重定向URL中解码原始URL:', decodedUrl);
        return extractWeiboIdFromUrl(decodedUrl); // 递归调用处理原始URL
      }
    } catch (e) {
      if (DEBUG) console.log('解码重定向URL失败:', e.message);
    }
  }
  
  // 匹配 weibo.com/用户ID/微博ID 格式（图文链接常见格式）
  const userPostMatch = url.match(/weibo\.com\/(\d+)\/(\d{13,})/);
  if (userPostMatch) {
    const postId = userPostMatch[2];
    if (DEBUG) console.log('✅ 提取到图文微博ID:', postId);
    return postId;
  }
  
  // 匹配 /detail/xxxxxxxx 或 /status/xxxxxxxx
  const mDetail = url.match(/\/(?:detail|status)\/(\d{13,})/);
  if (mDetail) {
    if (DEBUG) console.log('✅ 提取到detail格式ID:', mDetail[1]);
    return mDetail[1];
  }
  
  // 尝试从URL路径中提取其他格式的ID
  const generalMatch = url.match(/\/(\d{13,})/);
  if (generalMatch) {
    if (DEBUG) console.log('✅ 提取到通用格式ID:', generalMatch[1]);
    return generalMatch[1];
  }
  
  if (DEBUG) console.log('❌ 未能提取到有效的微博ID');
  return '';
}

function extractFullOidFromUrl(url) {
  if (!url) return '';
  
  if (DEBUG) console.log('提取视频OID，原始URL:', url);
  
  // 首先尝试从URL参数中解码原始URL（处理重定向情况）
  if (url.includes('passport.weibo.com/visitor/visitor') && url.includes('url=')) {
    try {
      const urlParam = url.match(/[?&]url=([^&]+)/);
      if (urlParam) {
        const decodedUrl = decodeURIComponent(urlParam[1]);
        if (DEBUG) console.log('🔍 OID提取：从重定向URL中解码原始URL:', decodedUrl);
        return extractFullOidFromUrl(decodedUrl); // 递归调用处理原始URL
      }
    } catch (e) {
      if (DEBUG) console.log('OID提取：解码重定向URL失败:', e.message);
    }
  }
  
  // 直接匹配完整的 1034:xxxxxxxx 格式（视频链接特有）
  const fullMatch = url.match(/1034:(\d{13,})/);
  if (fullMatch) {
    const fullOid = `1034:${fullMatch[1]}`;
    if (DEBUG) console.log('✅ 提取到完整视频OID:', fullOid);
    return fullOid;
  }
  
  if (DEBUG) console.log('❌ 未能提取到有效的视频OID');
  return '';
}

// 测试用例
console.log('=== 测试图文链接ID提取 ===');
const imageUrl = 'https://weibo.com/6161876013/5198058146499145';
const imageId = extractWeiboIdFromUrl(imageUrl);
console.log('图文链接:', imageUrl);
console.log('提取到的ID:', imageId);
console.log('期望的ID: 5198058146499145');
console.log('提取正确:', imageId === '5198058146499145');

console.log('\n=== 测试视频链接OID提取 ===');
const videoUrl = 'https://video.weibo.com/show?fid=1034:5190831422767136';
const videoOid = extractFullOidFromUrl(videoUrl);
console.log('视频链接:', videoUrl);
console.log('提取到的OID:', videoOid);
console.log('期望的OID: 1034:5190831422767136');
console.log('提取正确:', videoOid === '1034:5190831422767136');
